import os
import os
import sys
from pathlib import Path
from contextlib import asynccontextmanager
from fastapi import FastAPI, HTTPException, Request, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, FileResponse
from fastapi.staticfiles import StaticFiles
from loguru import logger

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from backend.core.config import settings
from backend.core.websocket import websocket_manager
from backend.database.database import get_db_manager
from backend.database.migrations import run_migrations
from backend.database.seeds import seed_database
from backend.routers import serial, protocols, interfaces, flows, projects
from backend.utils.logger import setup_logger

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用程序生命周期管理"""
    # 启动时执行
    logger.info("启动串口通信测试应用程序")
    
    try:
        # 初始化数据库
        db_manager = get_db_manager()
        logger.info("数据库初始化完成")
        
        # 运行数据库迁移
        migration_result = run_migrations()
        if migration_result["success"]:
            logger.info(f"数据库迁移完成: {migration_result['applied']} 个迁移已应用")
        else:
            logger.warning(f"数据库迁移警告: {migration_result['message']}")
        
        # 检查是否需要填充示例数据
        projects = db_manager.execute_query("SELECT COUNT(*) as count FROM projects", fetch_one=True)
        if projects and projects['count'] == 0:
            logger.info("检测到空数据库，正在创建示例数据...")
            seed_result = seed_database()
            if seed_result["success"]:
                logger.info("示例数据创建完成")
            else:
                logger.warning(f"示例数据创建失败: {seed_result['message']}")
        
        # 初始化WebSocket管理器
        await websocket_manager.startup()
        logger.info("WebSocket管理器初始化完成")
        
    except Exception as e:
        logger.error(f"应用程序启动失败: {e}")
        raise
    
    yield
    
    # 关闭时执行
    logger.info("正在关闭串口通信测试应用程序")
    
    try:
        # 关闭WebSocket管理器
        await websocket_manager.shutdown()
        logger.info("WebSocket管理器已关闭")
        
        # 关闭数据库连接
        db_manager = get_db_manager()
        db_manager.close()
        logger.info("数据库连接已关闭")
        
    except Exception as e:
        logger.error(f"应用程序关闭时出错: {e}")


def create_app() -> FastAPI:
    """创建FastAPI应用程序实例"""
    
    # 设置日志
    setup_logger(
        log_level=settings.LOG_LEVEL,
        log_file=settings.LOG_FILE,
        log_rotation=settings.LOG_MAX_SIZE,
        log_retention=settings.LOG_BACKUP_COUNT
    )
    
    # 创建FastAPI应用
    app = FastAPI(
        title=settings.APP_NAME,
        description="串口通信测试工具的后端API服务",
        version=settings.VERSION,
        docs_url="/docs" if settings.DEBUG else None,
        redoc_url="/redoc" if settings.DEBUG else None,
        lifespan=lifespan
    )
    
    # 配置CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.ALLOWED_ORIGINS,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # 注册路由
    app.include_router(serial.router, prefix="/api/serial", tags=["串口通信"])
    app.include_router(protocols.router, prefix="/api/protocols", tags=["协议管理"])
    app.include_router(interfaces.router, prefix="/api/interfaces", tags=["界面设计"])
    app.include_router(flows.router, prefix="/api/flows", tags=["流程引擎"])
    app.include_router(projects.router, prefix="/api/projects", tags=["项目管理"])
    
    # WebSocket路由
    @app.websocket("/ws")
    async def websocket_endpoint(websocket):
        await websocket_manager.connect(websocket)
    
    # 静态文件服务（用于前端）
    frontend_dir = project_root / "frontend" / "dist"
    if frontend_dir.exists():
        app.mount("/static", StaticFiles(directory=str(frontend_dir)), name="static")
        
        @app.get("/")
        async def serve_frontend():
            index_file = frontend_dir / "index.html"
            if index_file.exists():
                return FileResponse(str(index_file))
            else:
                raise HTTPException(status_code=404, detail="前端文件未找到")
        
        @app.get("/{path:path}")
        async def serve_frontend_routes(path: str):
            # 对于前端路由，返回index.html
            if not path.startswith("api/") and not path.startswith("docs") and not path.startswith("redoc"):
                index_file = frontend_dir / "index.html"
                if index_file.exists():
                    return FileResponse(str(index_file))
            raise HTTPException(status_code=404, detail="页面未找到")
    
    # 健康检查端点
    @app.get("/health")
    async def health_check():
        """健康检查"""
        try:
            # 检查数据库连接
            db_manager = get_db_manager()
            db_manager.execute_query("SELECT 1", fetch_one=True)
            
            return {
                "status": "healthy",
                "version": settings.VERSION,
                "database": "connected",
                "websocket": "active" if websocket_manager.active_connections else "inactive"
            }
        except Exception as e:
            logger.error(f"健康检查失败: {e}")
            raise HTTPException(status_code=503, detail="服务不可用")
    
    # 应用信息端点
    @app.get("/api/info")
    async def app_info():
        """获取应用程序信息"""
        return {
            "name": settings.APP_NAME,
            "version": settings.VERSION,
            "debug": settings.DEBUG,
            "environment": settings.ENVIRONMENT,
            "database_url": settings.DATABASE_URL,
            "websocket_connections": len(websocket_manager.active_connections)
        }
    
    return app

# 创建应用程序实例
app = create_app()


if __name__ == "__main__":
    import uvicorn
    
    # 开发模式运行
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower(),
        access_log=True
    )