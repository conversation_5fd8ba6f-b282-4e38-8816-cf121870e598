from typing import List, Optional
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from loguru import logger

from backend.models.base import ResponseModel, InterfaceDesign, ComponentConfig, PaginationModel
from backend.services.interface_service import InterfaceService

router = APIRouter()

# 获取界面服务实例
def get_interface_service() -> InterfaceService:
    return InterfaceService()


class InterfaceCreateRequest(BaseModel):
    """界面创建请求模型"""
    name: str
    description: str = ""
    components: List[ComponentConfig] = []
    layout: dict = {}


class InterfaceUpdateRequest(BaseModel):
    """界面更新请求模型"""
    name: Optional[str] = None
    description: Optional[str] = None
    components: Optional[List[ComponentConfig]] = None
    layout: Optional[dict] = None


class ComponentUpdateRequest(BaseModel):
    """组件更新请求模型"""
    component: ComponentConfig


@router.get("/", response_model=ResponseModel)
async def get_interfaces(
    page: int = 1,
    page_size: int = 10,
    search: Optional[str] = None,
    interface_service: InterfaceService = Depends(get_interface_service)
):
    """获取界面列表"""
    try:
        interfaces, total = await interface_service.get_interfaces(
            page=page, page_size=page_size, search=search
        )
        pagination = PaginationModel(
            page=page,
            page_size=page_size,
            total=total,
            total_pages=(total + page_size - 1) // page_size
        )
        return ResponseModel(
            data={"interfaces": interfaces, "pagination": pagination},
            message="获取界面列表成功"
        )
    except Exception as e:
        logger.error(f"获取界面列表失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{interface_id}", response_model=ResponseModel)
async def get_interface(
    interface_id: str,
    interface_service: InterfaceService = Depends(get_interface_service)
):
    """获取界面详情"""
    try:
        interface = await interface_service.get_interface(interface_id)
        if not interface:
            raise HTTPException(status_code=404, detail="界面不存在")
        return ResponseModel(data=interface, message="获取界面详情成功")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取界面详情失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/", response_model=ResponseModel)
async def create_interface(
    request: InterfaceCreateRequest,
    interface_service: InterfaceService = Depends(get_interface_service)
):
    """创建界面"""
    try:
        interface = await interface_service.create_interface(
            name=request.name,
            description=request.description,
            components=request.components,
            layout=request.layout
        )
        return ResponseModel(data=interface, message="界面创建成功")
    except Exception as e:
        logger.error(f"界面创建失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/{interface_id}", response_model=ResponseModel)
async def update_interface(
    interface_id: str,
    request: InterfaceUpdateRequest,
    interface_service: InterfaceService = Depends(get_interface_service)
):
    """更新界面"""
    try:
        interface = await interface_service.update_interface(
            interface_id, request.dict(exclude_unset=True)
        )
        if not interface:
            raise HTTPException(status_code=404, detail="界面不存在")
        return ResponseModel(data=interface, message="界面更新成功")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"界面更新失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{interface_id}", response_model=ResponseModel)
async def delete_interface(
    interface_id: str,
    interface_service: InterfaceService = Depends(get_interface_service)
):
    """删除界面"""
    try:
        success = await interface_service.delete_interface(interface_id)
        if not success:
            raise HTTPException(status_code=404, detail="界面不存在")
        return ResponseModel(message="界面删除成功")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"界面删除失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{interface_id}/components", response_model=ResponseModel)
async def get_interface_components(
    interface_id: str,
    interface_service: InterfaceService = Depends(get_interface_service)
):
    """获取界面组件列表"""
    try:
        components = await interface_service.get_interface_components(interface_id)
        if components is None:
            raise HTTPException(status_code=404, detail="界面不存在")
        return ResponseModel(data=components, message="获取界面组件成功")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取界面组件失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{interface_id}/components", response_model=ResponseModel)
async def add_component(
    interface_id: str,
    component: ComponentConfig,
    interface_service: InterfaceService = Depends(get_interface_service)
):
    """添加组件"""
    try:
        success = await interface_service.add_component(interface_id, component)
        if not success:
            raise HTTPException(status_code=404, detail="界面不存在")
        return ResponseModel(message="组件添加成功")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"组件添加失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/{interface_id}/components/{component_id}", response_model=ResponseModel)
async def update_component(
    interface_id: str,
    component_id: str,
    request: ComponentUpdateRequest,
    interface_service: InterfaceService = Depends(get_interface_service)
):
    """更新组件"""
    try:
        success = await interface_service.update_component(
            interface_id, component_id, request.component
        )
        if not success:
            raise HTTPException(status_code=404, detail="界面或组件不存在")
        return ResponseModel(message="组件更新成功")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"组件更新失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{interface_id}/components/{component_id}", response_model=ResponseModel)
async def delete_component(
    interface_id: str,
    component_id: str,
    interface_service: InterfaceService = Depends(get_interface_service)
):
    """删除组件"""
    try:
        success = await interface_service.delete_component(interface_id, component_id)
        if not success:
            raise HTTPException(status_code=404, detail="界面或组件不存在")
        return ResponseModel(message="组件删除成功")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"组件删除失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{interface_id}/duplicate", response_model=ResponseModel)
async def duplicate_interface(
    interface_id: str,
    new_name: str,
    interface_service: InterfaceService = Depends(get_interface_service)
):
    """复制界面"""
    try:
        interface = await interface_service.duplicate_interface(interface_id, new_name)
        if not interface:
            raise HTTPException(status_code=404, detail="源界面不存在")
        return ResponseModel(data=interface, message="界面复制成功")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"界面复制失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{interface_id}/preview", response_model=ResponseModel)
async def preview_interface(
    interface_id: str,
    interface_service: InterfaceService = Depends(get_interface_service)
):
    """预览界面"""
    try:
        preview_data = await interface_service.preview_interface(interface_id)
        if not preview_data:
            raise HTTPException(status_code=404, detail="界面不存在")
        return ResponseModel(data=preview_data, message="界面预览数据获取成功")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"界面预览失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{interface_id}/validate", response_model=ResponseModel)
async def validate_interface(
    interface_id: str,
    interface_service: InterfaceService = Depends(get_interface_service)
):
    """验证界面配置"""
    try:
        result = await interface_service.validate_interface(interface_id)
        return ResponseModel(data=result, message="界面验证完成")
    except Exception as e:
        logger.error(f"界面验证失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{interface_id}/export", response_model=ResponseModel)
async def export_interface(
    interface_id: str,
    format: str = "json",  # json, html, vue, react
    interface_service: InterfaceService = Depends(get_interface_service)
):
    """导出界面"""
    try:
        result = await interface_service.export_interface(interface_id, format)
        if not result:
            raise HTTPException(status_code=404, detail="界面不存在")
        return ResponseModel(data=result, message="界面导出成功")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"界面导出失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/import", response_model=ResponseModel)
async def import_interface(
    data: str,
    format: str = "json",
    interface_service: InterfaceService = Depends(get_interface_service)
):
    """导入界面"""
    try:
        interface = await interface_service.import_interface(data, format)
        return ResponseModel(data=interface, message="界面导入成功")
    except Exception as e:
        logger.error(f"界面导入失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/templates/components", response_model=ResponseModel)
async def get_component_templates(
    category: Optional[str] = None,
    interface_service: InterfaceService = Depends(get_interface_service)
):
    """获取组件模板"""
    try:
        templates = await interface_service.get_component_templates(category)
        return ResponseModel(data=templates, message="获取组件模板成功")
    except Exception as e:
        logger.error(f"获取组件模板失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{interface_id}/layout", response_model=ResponseModel)
async def update_layout(
    interface_id: str,
    layout: dict,
    interface_service: InterfaceService = Depends(get_interface_service)
):
    """更新界面布局"""
    try:
        success = await interface_service.update_layout(interface_id, layout)
        if not success:
            raise HTTPException(status_code=404, detail="界面不存在")
        return ResponseModel(message="布局更新成功")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"布局更新失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{interface_id}/data-bindings", response_model=ResponseModel)
async def get_data_bindings(
    interface_id: str,
    interface_service: InterfaceService = Depends(get_interface_service)
):
    """获取界面数据绑定"""
    try:
        bindings = await interface_service.get_data_bindings(interface_id)
        if bindings is None:
            raise HTTPException(status_code=404, detail="界面不存在")
        return ResponseModel(data=bindings, message="获取数据绑定成功")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取数据绑定失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))