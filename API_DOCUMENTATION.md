# API Documentation

This document provides a detailed overview of all available API endpoints.

## 1. Project Management (`/api/projects`)

- **GET /**: Get a list of projects.
- **GET /{project_id}**: Get details of a specific project.
- **POST /**: Create a new project.
- **PUT /{project_id}**: Update an existing project.
- **DELETE /{project_id}**: Delete a project.
- **POST /{project_id}/duplicate**: Duplicate a project.
- **GET /{project_id}/export**: Export a project.

## 2. Protocol Management (`/api/protocols`)

- **GET /**: Get a list of protocols.
- **GET /{protocol_id}**: Get details of a specific protocol.
- **POST /**: Create a new protocol.
- **PUT /{protocol_id}**: Update an existing protocol.
- **DELETE /{protocol_id}**: Delete a protocol.
- **POST /test**: Test a protocol.
- **POST /{protocol_id}/parse**: Parse data using a protocol.
- **POST /{protocol_id}/validate**: Validate a protocol configuration.
- **POST /{protocol_id}/duplicate**: Duplicate a protocol.

## 3. Serial Port Communication (`/api/serial`)

- **GET /ports**: Get a list of available serial ports.
- **GET /status**: Get the status of the serial port.
- **POST /connect**: Connect to a serial port.
- **POST /disconnect**: Disconnect from the serial port.
- **POST /send**: Send data to the serial port.
- **GET /history**: Get the history of serial data.
- **DELETE /history**: Clear the history of serial data.
- **GET /statistics**: Get statistics of the serial port.
- **POST /test**: Test the serial port connection.
- **GET /config**: Get the current configuration of the serial port.
- **PUT /config**: Update the configuration of the serial port.

## 4. Flow Management (`/api/flows`)

- **GET /**: Get a list of flows.
- **GET /{flow_id}**: Get details of a specific flow.
- **POST /**: Create a new flow.
- **PUT /{flow_id}**: Update an existing flow.
- **DELETE /{flow_id}**: Delete a flow.
- **POST /{flow_id}/execute**: Execute a flow.
- **POST /control**: Control a flow execution (start, pause, resume, stop).
- **GET /{flow_id}/executions**: Get a list of executions for a flow.

## 5. Interface Management (`/api/interfaces`)

- **GET /**: Get a list of interfaces.
- **GET /{interface_id}**: Get details of a specific interface.
- **POST /**: Create a new interface.
- **PUT /{interface_id}**: Update an existing interface.
- **DELETE /{interface_id}**: Delete an interface.
- **GET /{interface_id}/components**: Get a list of components for an interface.
- **POST /{interface_id}/components**: Add a component to an interface.
- **PUT /{interface_id}/components/{component_id}**: Update a component in an interface.
- **DELETE /{interface_id}/components/{component_id}**: Delete a component from an interface.