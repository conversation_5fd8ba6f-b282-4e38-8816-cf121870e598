[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "serial-communication-tool"
version = "1.0.0"
description = "串口通信测试工具"
authors = [{name = "Your Name", email = "<EMAIL>"}]
license = {text = "MIT"}
readme = "../README.md"
requires-python = ">=3.8"
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Framework :: FastAPI",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Communications",
    "Topic :: System :: Hardware :: Hardware Drivers",
]
keywords = ["serial", "communication", "testing", "fastapi", "websocket"]

[project.urls]
Homepage = "https://github.com/yourusername/serial-communication-tool"
Repository = "https://github.com/yourusername/serial-communication-tool.git"
Issues = "https://github.com/yourusername/serial-communication-tool/issues"
Documentation = "https://github.com/yourusername/serial-communication-tool/wiki"

[tool.setuptools.packages.find]
where = ["."]  # list of folders that contain the packages (["src"] by default)
include = ["*"]  # package names should match these glob patterns (["*"] by default)
exclude = ["tests*"]  # exclude packages matching these glob patterns

[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
# A regex preceded by ^/ will apply only to files and directories
# in the root of the project.
^/migrations/
'''

[tool.isort]
profile = "black"
line_length = 88
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true

[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503"]
exclude = [
    ".git",
    "__pycache__",
    "migrations",
    ".venv",
    "venv",
    "build",
    "dist"
]

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "serial.*",
    "loguru.*",
    "uvicorn.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m "not slow"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]
filterwarnings = [
    "error",
    "ignore::UserWarning",
    "ignore::DeprecationWarning",
]

[tool.coverage.run]
source = ["."]  # 覆盖率检查的源码目录
omit = [
    "*/tests/*",
    "*/test_*.py",
    "*/__pycache__/*",
    "*/migrations/*",
    "*/venv/*",
    "*/.venv/*",
    "setup.py",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\bProtocol\):",
    "@(abc\.)?abstractmethod",
]
ignore_errors = true

[tool.coverage.html]
directory = "htmlcov"