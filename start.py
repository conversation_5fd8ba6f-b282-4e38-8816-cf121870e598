#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目启动脚本

这个脚本用于启动串口通信测试工具的后端服务。
支持不同的运行模式：开发、生产、测试。
"""

import os
import sys
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def setup_environment(env_type: str):
    """设置环境变量"""
    env_configs = {
        "development": {
            "ENVIRONMENT": "development",
            "DEBUG": "true",
            "LOG_LEVEL": "DEBUG",
            "HOST": "127.0.0.1",
            "PORT": "8000"
        },
        "production": {
            "ENVIRONMENT": "production",
            "DEBUG": "false",
            "LOG_LEVEL": "INFO",
            "HOST": "0.0.0.0",
            "PORT": "8000"
        },
        "testing": {
            "ENVIRONMENT": "testing",
            "DEBUG": "true",
            "LOG_LEVEL": "DEBUG",
            "DATABASE_URL": "sqlite:///./test.db",
            "HOST": "127.0.0.1",
            "PORT": "8001"
        }
    }
    
    config = env_configs.get(env_type, env_configs["development"])
    for key, value in config.items():
        os.environ[key] = value


def check_dependencies():
    """检查依赖项"""
    try:
        import uvicorn
        import fastapi
        import loguru
        import pydantic
        print("✓ 所有依赖项已安装")
        return True
    except ImportError as e:
        print(f"✗ 缺少依赖项: {e}")
        print("请运行: pip install -r backend/requirements.txt")
        return False


def create_directories():
    """创建必要的目录"""
    directories = [
        "data",
        "logs",
        "uploads",
        "backups"
    ]
    
    for directory in directories:
        dir_path = project_root / directory
        dir_path.mkdir(exist_ok=True)
        print(f"✓ 目录已创建: {directory}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="串口通信测试工具启动脚本")
    parser.add_argument(
        "--env",
        choices=["development", "production", "testing"],
        default="development",
        help="运行环境 (默认: development)"
    )
    parser.add_argument(
        "--host",
        default=None,
        help="服务器主机地址"
    )
    parser.add_argument(
        "--port",
        type=int,
        default=None,
        help="服务器端口"
    )
    parser.add_argument(
        "--reload",
        action="store_true",
        help="启用自动重载 (仅开发模式)"
    )
    parser.add_argument(
        "--workers",
        type=int,
        default=1,
        help="工作进程数 (仅生产模式)"
    )
    parser.add_argument(
        "--check",
        action="store_true",
        help="仅检查环境，不启动服务"
    )
    
    args = parser.parse_args()
    
    print("=" * 50)
    print("串口通信测试工具")
    print("=" * 50)
    
    # 设置环境
    setup_environment(args.env)
    print(f"✓ 环境设置: {args.env}")
    
    # 检查依赖项
    if not check_dependencies():
        sys.exit(1)
    
    # 创建目录
    create_directories()
    
    if args.check:
        print("✓ 环境检查完成")
        return
    
    # 导入应用
    try:
        from backend.main import app
        print("✓ 应用程序加载成功")
    except Exception as e:
        print(f"✗ 应用程序加载失败: {e}")
        sys.exit(1)
    
    # 启动服务器
    import uvicorn
    
    # 配置参数
    host = args.host or os.getenv("HOST", "127.0.0.1")
    port = args.port or int(os.getenv("PORT", "8000"))
    reload = args.reload or (args.env == "development" and os.getenv("DEBUG", "false").lower() == "true")
    log_level = os.getenv("LOG_LEVEL", "info").lower()
    
    print(f"\n启动服务器...")
    print(f"环境: {args.env}")
    print(f"地址: http://{host}:{port}")
    print(f"重载: {reload}")
    print(f"日志级别: {log_level}")
    
    if args.env == "production" and args.workers > 1:
        # 生产模式使用多进程
        uvicorn.run(
            "backend.main:app",
            host=host,
            port=port,
            workers=args.workers,
            log_level=log_level,
            access_log=True
        )
    else:
        # 开发/测试模式
        uvicorn.run(
            "backend.main:app",
            host=host,
            port=port,
            reload=reload,
            log_level=log_level,
            access_log=True
        )


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n服务器已停止")
    except Exception as e:
        print(f"\n启动失败: {e}")
        sys.exit(1)