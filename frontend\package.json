{"name": "serial-config-frontend", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "antd": "^5.12.8", "@ant-design/icons": "^5.2.6", "zustand": "^4.4.7", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-grid-layout": "^1.4.4", "reactflow": "^11.10.4", "echarts": "^5.4.3", "echarts-for-react": "^3.0.2", "axios": "^1.6.2", "react-router-dom": "^6.20.1", "dayjs": "^1.11.10"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@types/react-grid-layout": "^1.3.5", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "typescript": "^5.2.2", "vite": "^5.0.8"}}