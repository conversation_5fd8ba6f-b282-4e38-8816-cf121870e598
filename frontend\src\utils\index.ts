// 数据格式转换工具
export const formatUtils = {
  // ASCII转HEX
  asciiToHex: (ascii: string): string => {
    return ascii
      .split('')
      .map(char => char.charCodeAt(0).toString(16).padStart(2, '0').toUpperCase())
      .join(' ')
  },

  // HEX转ASCII
  hexToAscii: (hex: string): string => {
    const hexArray = hex.replace(/\s+/g, '').match(/.{1,2}/g) || []
    return hexArray
      .map(byte => String.fromCharCode(parseInt(byte, 16)))
      .join('')
  },

  // 验证HEX格式
  isValidHex: (hex: string): boolean => {
    const cleanHex = hex.replace(/\s+/g, '')
    return /^[0-9A-Fa-f]*$/.test(cleanHex) && cleanHex.length % 2 === 0
  },

  // 格式化HEX显示
  formatHex: (hex: string): string => {
    const cleanHex = hex.replace(/\s+/g, '').toUpperCase()
    return cleanHex.match(/.{1,2}/g)?.join(' ') || ''
  },

  // 数值转换
  numberToBytes: (value: number, byteLength: number, isLittleEndian = false): number[] => {
    const bytes: number[] = []
    for (let i = 0; i < byteLength; i++) {
      bytes.push((value >> (i * 8)) & 0xFF)
    }
    return isLittleEndian ? bytes : bytes.reverse()
  },

  // 字节数组转数值
  bytesToNumber: (bytes: number[], isLittleEndian = false): number => {
    const orderedBytes = isLittleEndian ? bytes : [...bytes].reverse()
    return orderedBytes.reduce((acc, byte, index) => acc + (byte << (index * 8)), 0)
  },
}

// 时间工具
export const timeUtils = {
  // 格式化时间戳
  formatTimestamp: (timestamp: string | number | Date, format = 'YYYY-MM-DD HH:mm:ss'): string => {
    const date = new Date(timestamp)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')
    const milliseconds = String(date.getMilliseconds()).padStart(3, '0')

    return format
      .replace('YYYY', String(year))
      .replace('MM', month)
      .replace('DD', day)
      .replace('HH', hours)
      .replace('mm', minutes)
      .replace('ss', seconds)
      .replace('SSS', milliseconds)
  },

  // 获取相对时间
  getRelativeTime: (timestamp: string | number | Date): string => {
    const now = new Date().getTime()
    const time = new Date(timestamp).getTime()
    const diff = now - time

    const minute = 60 * 1000
    const hour = 60 * minute
    const day = 24 * hour
    const week = 7 * day
    const month = 30 * day
    const year = 365 * day

    if (diff < minute) return '刚刚'
    if (diff < hour) return `${Math.floor(diff / minute)}分钟前`
    if (diff < day) return `${Math.floor(diff / hour)}小时前`
    if (diff < week) return `${Math.floor(diff / day)}天前`
    if (diff < month) return `${Math.floor(diff / week)}周前`
    if (diff < year) return `${Math.floor(diff / month)}个月前`
    return `${Math.floor(diff / year)}年前`
  },

  // 延时函数
  delay: (ms: number): Promise<void> => {
    return new Promise(resolve => setTimeout(resolve, ms))
  },
}

// 文件工具
export const fileUtils = {
  // 下载文件
  downloadFile: (content: string, filename: string, contentType = 'text/plain'): void => {
    const blob = new Blob([content], { type: contentType })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = filename
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  },

  // 读取文件内容
  readFileAsText: (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = (e) => resolve(e.target?.result as string)
      reader.onerror = (e) => reject(e)
      reader.readAsText(file)
    })
  },

  // 获取文件扩展名
  getFileExtension: (filename: string): string => {
    return filename.split('.').pop()?.toLowerCase() || ''
  },

  // 格式化文件大小
  formatFileSize: (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  },
}

// 验证工具
export const validationUtils = {
  // 验证邮箱
  isValidEmail: (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  },

  // 验证IP地址
  isValidIP: (ip: string): boolean => {
    const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/
    return ipRegex.test(ip)
  },

  // 验证端口号
  isValidPort: (port: number | string): boolean => {
    const portNum = typeof port === 'string' ? parseInt(port, 10) : port
    return Number.isInteger(portNum) && portNum >= 1 && portNum <= 65535
  },

  // 验证串口名称
  isValidSerialPort: (port: string): boolean => {
    // Windows: COM1, COM2, etc.
    // Linux/Mac: /dev/ttyUSB0, /dev/ttyACM0, etc.
    const windowsRegex = /^COM\d+$/i
    const unixRegex = /^\/dev\/(tty(USB|ACM|S)\d+|cu\..+)$/
    return windowsRegex.test(port) || unixRegex.test(port)
  },
}

// 数据处理工具
export const dataUtils = {
  // 深拷贝
  deepClone: <T>(obj: T): T => {
    if (obj === null || typeof obj !== 'object') return obj
    if (obj instanceof Date) return new Date(obj.getTime()) as unknown as T
    if (obj instanceof Array) return obj.map(item => dataUtils.deepClone(item)) as unknown as T
    if (typeof obj === 'object') {
      const clonedObj = {} as T
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          clonedObj[key] = dataUtils.deepClone(obj[key])
        }
      }
      return clonedObj
    }
    return obj
  },

  // 防抖
  debounce: <T extends (...args: any[]) => any>(
    func: T,
    wait: number
  ): (...args: Parameters<T>) => void => {
    let timeout: number
    return (...args: Parameters<T>) => {
      clearTimeout(timeout)
      timeout = setTimeout(() => func.apply(null, args), wait)
    }
  },

  // 节流
  throttle: <T extends (...args: any[]) => any>(
    func: T,
    limit: number
  ): (...args: Parameters<T>) => void => {
    let inThrottle: boolean
    return (...args: Parameters<T>) => {
      if (!inThrottle) {
        func.apply(null, args)
        inThrottle = true
        setTimeout(() => (inThrottle = false), limit)
      }
    }
  },

  // 生成唯一ID
  generateId: (): string => {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  },

  // 数组去重
  uniqueArray: <T>(array: T[], key?: keyof T): T[] => {
    if (!key) {
      return [...new Set(array)]
    }
    const seen = new Set()
    return array.filter(item => {
      const value = item[key]
      if (seen.has(value)) {
        return false
      }
      seen.add(value)
      return true
    })
  },
}

// 颜色工具
export const colorUtils = {
  // HEX转RGB
  hexToRgb: (hex: string): { r: number; g: number; b: number } | null => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
    return result
      ? {
          r: parseInt(result[1], 16),
          g: parseInt(result[2], 16),
          b: parseInt(result[3], 16),
        }
      : null
  },

  // RGB转HEX
  rgbToHex: (r: number, g: number, b: number): string => {
    return '#' + [r, g, b].map(x => {
      const hex = x.toString(16)
      return hex.length === 1 ? '0' + hex : hex
    }).join('')
  },

  // 生成随机颜色
  randomColor: (): string => {
    return '#' + Math.floor(Math.random() * 16777215).toString(16).padStart(6, '0')
  },

  // 获取对比色
  getContrastColor: (hex: string): string => {
    const rgb = colorUtils.hexToRgb(hex)
    if (!rgb) return '#000000'
    
    const brightness = (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000
    return brightness > 128 ? '#000000' : '#ffffff'
  },
}

// 导出所有工具
export default {
  formatUtils,
  timeUtils,
  fileUtils,
  validationUtils,
  dataUtils,
  colorUtils,
}