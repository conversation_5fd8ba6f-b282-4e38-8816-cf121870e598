from typing import List, Optional
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from loguru import logger

from backend.models.base import (
    ResponseModel, Flow, FlowNode, FlowEdge, FlowExecution, 
    FlowStatus, PaginationModel
)
from backend.services.flow_service import FlowService

router = APIRouter()

# 获取流程服务实例
def get_flow_service() -> FlowService:
    return FlowService()


class FlowCreateRequest(BaseModel):
    """流程创建请求模型"""
    name: str
    description: str = ""
    nodes: List[FlowNode] = []
    edges: List[FlowEdge] = []
    variables: dict = {}


class FlowUpdateRequest(BaseModel):
    """流程更新请求模型"""
    name: Optional[str] = None
    description: Optional[str] = None
    nodes: Optional[List[FlowNode]] = None
    edges: Optional[List[FlowEdge]] = None
    variables: Optional[dict] = None
    is_active: Optional[bool] = None


class FlowExecuteRequest(BaseModel):
    """流程执行请求模型"""
    variables: dict = {}
    start_node: Optional[str] = None


class FlowControlRequest(BaseModel):
    """流程控制请求模型"""
    action: str  # start, pause, resume, stop
    execution_id: Optional[str] = None


@router.get("/", response_model=ResponseModel)
async def get_flows(
    page: int = 1,
    page_size: int = 10,
    search: Optional[str] = None,
    is_active: Optional[bool] = None,
    flow_service: FlowService = Depends(get_flow_service)
):
    """获取流程列表"""
    try:
        flows, total = await flow_service.get_flows(
            page=page, page_size=page_size, search=search, is_active=is_active
        )
        pagination = PaginationModel(
            page=page,
            page_size=page_size,
            total=total,
            total_pages=(total + page_size - 1) // page_size
        )
        return ResponseModel(
            data={"flows": flows, "pagination": pagination},
            message="获取流程列表成功"
        )
    except Exception as e:
        logger.error(f"获取流程列表失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{flow_id}", response_model=ResponseModel)
async def get_flow(
    flow_id: str,
    flow_service: FlowService = Depends(get_flow_service)
):
    """获取流程详情"""
    try:
        flow = await flow_service.get_flow(flow_id)
        if not flow:
            raise HTTPException(status_code=404, detail="流程不存在")
        return ResponseModel(data=flow, message="获取流程详情成功")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取流程详情失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/", response_model=ResponseModel)
async def create_flow(
    request: FlowCreateRequest,
    flow_service: FlowService = Depends(get_flow_service)
):
    """创建流程"""
    try:
        flow = await flow_service.create_flow(
            name=request.name,
            description=request.description,
            nodes=request.nodes,
            edges=request.edges,
            variables=request.variables
        )
        return ResponseModel(data=flow, message="流程创建成功")
    except Exception as e:
        logger.error(f"流程创建失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/{flow_id}", response_model=ResponseModel)
async def update_flow(
    flow_id: str,
    request: FlowUpdateRequest,
    flow_service: FlowService = Depends(get_flow_service)
):
    """更新流程"""
    try:
        flow = await flow_service.update_flow(flow_id, request.dict(exclude_unset=True))
        if not flow:
            raise HTTPException(status_code=404, detail="流程不存在")
        return ResponseModel(data=flow, message="流程更新成功")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"流程更新失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{flow_id}", response_model=ResponseModel)
async def delete_flow(
    flow_id: str,
    flow_service: FlowService = Depends(get_flow_service)
):
    """删除流程"""
    try:
        success = await flow_service.delete_flow(flow_id)
        if not success:
            raise HTTPException(status_code=404, detail="流程不存在")
        return ResponseModel(message="流程删除成功")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"流程删除失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{flow_id}/execute", response_model=ResponseModel)
async def execute_flow(
    flow_id: str,
    request: FlowExecuteRequest,
    flow_service: FlowService = Depends(get_flow_service)
):
    """执行流程"""
    try:
        execution = await flow_service.execute_flow(
            flow_id=flow_id,
            variables=request.variables,
            start_node=request.start_node
        )
        if not execution:
            raise HTTPException(status_code=404, detail="流程不存在")
        return ResponseModel(data=execution, message="流程执行已启动")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"流程执行失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/control", response_model=ResponseModel)
async def control_flow(
    request: FlowControlRequest,
    flow_service: FlowService = Depends(get_flow_service)
):
    """控制流程执行"""
    try:
        result = await flow_service.control_flow(
            action=request.action,
            execution_id=request.execution_id
        )
        return ResponseModel(data=result, message=f"流程{request.action}操作成功")
    except Exception as e:
        logger.error(f"流程控制失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{flow_id}/executions", response_model=ResponseModel)
async def get_flow_executions(
    flow_id: str,
    page: int = 1,
    page_size: int = 10,
    status: Optional[FlowStatus] = None,
    flow_service: FlowService = Depends(get_flow_service)
):
    """获取流程执行历史"""
    try:
        executions, total = await flow_service.get_flow_executions(
            flow_id=flow_id,
            page=page,
            page_size=page_size,
            status=status
        )
        pagination = PaginationModel(
            page=page,
            page_size=page_size,
            total=total,
            total_pages=(total + page_size - 1) // page_size
        )
        return ResponseModel(
            data={"executions": executions, "pagination": pagination},
            message="获取执行历史成功"
        )
    except Exception as e:
        logger.error(f"获取执行历史失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/executions/{execution_id}", response_model=ResponseModel)
async def get_execution(
    execution_id: str,
    flow_service: FlowService = Depends(get_flow_service)
):
    """获取执行详情"""
    try:
        execution = await flow_service.get_execution(execution_id)
        if not execution:
            raise HTTPException(status_code=404, detail="执行记录不存在")
        return ResponseModel(data=execution, message="获取执行详情成功")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取执行详情失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/executions/{execution_id}/logs", response_model=ResponseModel)
async def get_execution_logs(
    execution_id: str,
    page: int = 1,
    page_size: int = 50,
    flow_service: FlowService = Depends(get_flow_service)
):
    """获取执行日志"""
    try:
        logs, total = await flow_service.get_execution_logs(
            execution_id=execution_id,
            page=page,
            page_size=page_size
        )
        pagination = PaginationModel(
            page=page,
            page_size=page_size,
            total=total,
            total_pages=(total + page_size - 1) // page_size
        )
        return ResponseModel(
            data={"logs": logs, "pagination": pagination},
            message="获取执行日志成功"
        )
    except Exception as e:
        logger.error(f"获取执行日志失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{flow_id}/validate", response_model=ResponseModel)
async def validate_flow(
    flow_id: str,
    flow_service: FlowService = Depends(get_flow_service)
):
    """验证流程配置"""
    try:
        result = await flow_service.validate_flow(flow_id)
        return ResponseModel(data=result, message="流程验证完成")
    except Exception as e:
        logger.error(f"流程验证失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{flow_id}/duplicate", response_model=ResponseModel)
async def duplicate_flow(
    flow_id: str,
    new_name: str,
    flow_service: FlowService = Depends(get_flow_service)
):
    """复制流程"""
    try:
        flow = await flow_service.duplicate_flow(flow_id, new_name)
        if not flow:
            raise HTTPException(status_code=404, detail="源流程不存在")
        return ResponseModel(data=flow, message="流程复制成功")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"流程复制失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{flow_id}/export", response_model=ResponseModel)
async def export_flow(
    flow_id: str,
    format: str = "json",  # json, xml, yaml
    flow_service: FlowService = Depends(get_flow_service)
):
    """导出流程"""
    try:
        result = await flow_service.export_flow(flow_id, format)
        if not result:
            raise HTTPException(status_code=404, detail="流程不存在")
        return ResponseModel(data=result, message="流程导出成功")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"流程导出失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/import", response_model=ResponseModel)
async def import_flow(
    data: str,
    format: str = "json",
    flow_service: FlowService = Depends(get_flow_service)
):
    """导入流程"""
    try:
        flow = await flow_service.import_flow(data, format)
        return ResponseModel(data=flow, message="流程导入成功")
    except Exception as e:
        logger.error(f"流程导入失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/templates/nodes", response_model=ResponseModel)
async def get_node_templates(
    category: Optional[str] = None,
    flow_service: FlowService = Depends(get_flow_service)
):
    """获取节点模板"""
    try:
        templates = await flow_service.get_node_templates(category)
        return ResponseModel(data=templates, message="获取节点模板成功")
    except Exception as e:
        logger.error(f"获取节点模板失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/running", response_model=ResponseModel)
async def get_running_flows(
    flow_service: FlowService = Depends(get_flow_service)
):
    """获取正在运行的流程"""
    try:
        running_flows = await flow_service.get_running_flows()
        return ResponseModel(data=running_flows, message="获取运行中流程成功")
    except Exception as e:
        logger.error(f"获取运行中流程失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/statistics", response_model=ResponseModel)
async def get_flow_statistics(
    flow_service: FlowService = Depends(get_flow_service)
):
    """获取流程统计信息"""
    try:
        stats = await flow_service.get_flow_statistics()
        return ResponseModel(data=stats, message="获取流程统计成功")
    except Exception as e:
        logger.error(f"获取流程统计失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))