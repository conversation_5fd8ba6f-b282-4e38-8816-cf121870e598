from typing import Optional, Any, Dict, List
from datetime import datetime
from pydantic import BaseModel, Field
from enum import Enum


class ResponseModel(BaseModel):
    """API响应基础模型"""
    success: bool = True
    message: str = "操作成功"
    data: Optional[Any] = None
    error: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.now)


class PaginationModel(BaseModel):
    """分页模型"""
    page: int = Field(1, ge=1, description="页码")
    page_size: int = Field(10, ge=1, le=100, description="每页数量")
    total: Optional[int] = Field(None, description="总数量")
    total_pages: Optional[int] = Field(None, description="总页数")


class SerialPortConfig(BaseModel):
    """串口配置模型"""
    port: str = Field(..., description="串口名称")
    baudrate: int = Field(9600, description="波特率")
    bytesize: int = Field(8, description="数据位")
    parity: str = Field("N", description="校验位")
    stopbits: int = Field(1, description="停止位")
    timeout: float = Field(1.0, description="超时时间")


class SerialPortStatus(BaseModel):
    """串口状态模型"""
    is_connected: bool = False
    port: Optional[str] = None
    config: Optional[SerialPortConfig] = None
    last_activity: Optional[datetime] = None
    bytes_sent: int = 0
    bytes_received: int = 0


class SerialDataFormat(str, Enum):
    """串口数据格式"""
    ASCII = "ascii"
    HEX = "hex"
    BINARY = "binary"


class SerialData(BaseModel):
    """串口数据模型"""
    data: str = Field(..., description="数据内容")
    format: SerialDataFormat = Field(SerialDataFormat.ASCII, description="数据格式")
    direction: str = Field(..., description="数据方向: send/receive")
    timestamp: datetime = Field(default_factory=datetime.now)
    port: Optional[str] = Field(None, description="串口名称")


class ProtocolFieldType(str, Enum):
    """协议字段类型"""
    UINT8 = "uint8"
    UINT16 = "uint16"
    UINT32 = "uint32"
    INT8 = "int8"
    INT16 = "int16"
    INT32 = "int32"
    FLOAT = "float"
    DOUBLE = "double"
    STRING = "string"
    BYTES = "bytes"


class ProtocolField(BaseModel):
    """协议字段模型"""
    id: str = Field(..., description="字段ID")
    name: str = Field(..., description="字段名称")
    type: ProtocolFieldType = Field(..., description="字段类型")
    offset: int = Field(..., description="偏移量")
    length: int = Field(..., description="长度")
    scale: float = Field(1.0, description="比例系数")
    unit: str = Field("", description="单位")
    description: str = Field("", description="描述")
    min_value: Optional[float] = Field(None, description="最小值")
    max_value: Optional[float] = Field(None, description="最大值")


class Protocol(BaseModel):
    """协议模型"""
    id: str = Field(..., description="协议ID")
    name: str = Field(..., description="协议名称")
    description: str = Field("", description="协议描述")
    total_length: int = Field(..., description="协议总长度")
    header: str = Field("", description="帧头")
    footer: str = Field("", description="帧尾")
    fields: List[ProtocolField] = Field(default_factory=list, description="字段列表")
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)


class ComponentType(str, Enum):
    """界面组件类型"""
    TEXT = "text"
    NUMBER = "number"
    CHART = "chart"
    GAUGE = "gauge"
    BUTTON = "button"
    SWITCH = "switch"
    IMAGE = "image"
    TABLE = "table"


class ComponentConfig(BaseModel):
    """组件配置模型"""
    id: str = Field(..., description="组件ID")
    type: ComponentType = Field(..., description="组件类型")
    title: str = Field("", description="组件标题")
    x: int = Field(0, description="X坐标")
    y: int = Field(0, description="Y坐标")
    width: int = Field(1, description="宽度")
    height: int = Field(1, description="高度")
    data_binding: Optional[str] = Field(None, description="数据绑定")
    properties: Dict[str, Any] = Field(default_factory=dict, description="组件属性")
    events: Dict[str, Any] = Field(default_factory=dict, description="事件配置")


class InterfaceDesign(BaseModel):
    """界面设计模型"""
    id: str = Field(..., description="界面ID")
    name: str = Field(..., description="界面名称")
    description: str = Field("", description="界面描述")
    components: List[ComponentConfig] = Field(default_factory=list, description="组件列表")
    layout: Dict[str, Any] = Field(default_factory=dict, description="布局配置")
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)


class FlowNodeType(str, Enum):
    """流程节点类型"""
    START = "start"
    END = "end"
    CONDITION = "condition"
    ACTION = "action"
    DELAY = "delay"
    SERIAL_SEND = "serial_send"
    SERIAL_RECEIVE = "serial_receive"
    DATA_PROCESS = "data_process"


class FlowNode(BaseModel):
    """流程节点模型"""
    id: str = Field(..., description="节点ID")
    type: FlowNodeType = Field(..., description="节点类型")
    label: str = Field(..., description="节点标签")
    position: Dict[str, float] = Field(..., description="节点位置")
    config: Dict[str, Any] = Field(default_factory=dict, description="节点配置")
    inputs: List[str] = Field(default_factory=list, description="输入连接")
    outputs: List[str] = Field(default_factory=list, description="输出连接")


class FlowEdge(BaseModel):
    """流程连线模型"""
    id: str = Field(..., description="连线ID")
    source: str = Field(..., description="源节点ID")
    target: str = Field(..., description="目标节点ID")
    source_handle: Optional[str] = Field(None, description="源句柄")
    target_handle: Optional[str] = Field(None, description="目标句柄")
    label: Optional[str] = Field(None, description="连线标签")
    config: Dict[str, Any] = Field(default_factory=dict, description="连线配置")


class FlowStatus(str, Enum):
    """流程状态"""
    IDLE = "idle"
    RUNNING = "running"
    PAUSED = "paused"
    STOPPED = "stopped"
    ERROR = "error"
    COMPLETED = "completed"


class FlowExecution(BaseModel):
    """流程执行模型"""
    id: str = Field(..., description="执行ID")
    flow_id: str = Field(..., description="流程ID")
    status: FlowStatus = Field(FlowStatus.IDLE, description="执行状态")
    current_node: Optional[str] = Field(None, description="当前节点")
    variables: Dict[str, Any] = Field(default_factory=dict, description="变量")
    logs: List[Dict[str, Any]] = Field(default_factory=list, description="执行日志")
    started_at: Optional[datetime] = Field(None, description="开始时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")
    error_message: Optional[str] = Field(None, description="错误信息")


class Flow(BaseModel):
    """流程模型"""
    id: str = Field(..., description="流程ID")
    name: str = Field(..., description="流程名称")
    description: str = Field("", description="流程描述")
    nodes: List[FlowNode] = Field(default_factory=list, description="节点列表")
    edges: List[FlowEdge] = Field(default_factory=list, description="连线列表")
    variables: Dict[str, Any] = Field(default_factory=dict, description="流程变量")
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    is_active: bool = Field(True, description="是否激活")


class ProjectStatus(str, Enum):
    """项目状态"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    ARCHIVED = "archived"


class Project(BaseModel):
    """项目模型"""
    id: str = Field(..., description="项目ID")
    name: str = Field(..., description="项目名称")
    description: str = Field("", description="项目描述")
    status: ProjectStatus = Field(ProjectStatus.ACTIVE, description="项目状态")
    protocols: List[str] = Field(default_factory=list, description="协议ID列表")
    interfaces: List[str] = Field(default_factory=list, description="界面ID列表")
    flows: List[str] = Field(default_factory=list, description="流程ID列表")
    settings: Dict[str, Any] = Field(default_factory=dict, description="项目设置")
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    created_by: Optional[str] = Field(None, description="创建者")


class ProjectTemplate(BaseModel):
    """项目模板模型"""
    id: str = Field(..., description="模板ID")
    name: str = Field(..., description="模板名称")
    description: str = Field("", description="模板描述")
    category: str = Field("", description="模板分类")
    thumbnail: Optional[str] = Field(None, description="缩略图")
    protocols: List[Protocol] = Field(default_factory=list, description="协议模板")
    interfaces: List[InterfaceDesign] = Field(default_factory=list, description="界面模板")
    flows: List[Flow] = Field(default_factory=list, description="流程模板")
    created_at: datetime = Field(default_factory=datetime.now)
    is_public: bool = Field(True, description="是否公开")


class SystemStatus(BaseModel):
    """系统状态模型"""
    cpu_usage: float = Field(0.0, description="CPU使用率")
    memory_usage: float = Field(0.0, description="内存使用率")
    disk_usage: float = Field(0.0, description="磁盘使用率")
    serial_connections: int = Field(0, description="串口连接数")
    websocket_connections: int = Field(0, description="WebSocket连接数")
    running_flows: int = Field(0, description="运行中的流程数")
    uptime: float = Field(0.0, description="运行时间（秒）")
    timestamp: datetime = Field(default_factory=datetime.now)


class NotificationLevel(str, Enum):
    """通知级别"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    SUCCESS = "success"


class Notification(BaseModel):
    """通知模型"""
    id: str = Field(..., description="通知ID")
    title: str = Field(..., description="通知标题")
    message: str = Field(..., description="通知内容")
    level: NotificationLevel = Field(NotificationLevel.INFO, description="通知级别")
    data: Optional[Dict[str, Any]] = Field(None, description="附加数据")
    read: bool = Field(False, description="是否已读")
    created_at: datetime = Field(default_factory=datetime.now)
    expires_at: Optional[datetime] = Field(None, description="过期时间")