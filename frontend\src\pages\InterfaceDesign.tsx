import React, { useState } from 'react'
import { Card, Row, Col, Button, Space, Drawer, Form, Input, Select } from 'antd'
import { SettingOutlined, DeleteOutlined } from '@ant-design/icons'
import { Responsive, WidthProvider } from 'react-grid-layout'
import 'react-grid-layout/css/styles.css'
import 'react-resizable/css/styles.css'

const ResponsiveGridLayout = WidthProvider(Responsive)

interface Widget {
  id: string
  type: 'text' | 'number' | 'chart' | 'gauge' | 'button' | 'switch'
  title: string
  dataBinding?: string
  config: Record<string, any>
}

const InterfaceDesign: React.FC = () => {
  const [widgets, setWidgets] = useState<Widget[]>([])
  const [layouts, setLayouts] = useState<any>({})
  const [selectedWidget, setSelectedWidget] = useState<Widget | null>(null)
  const [drawerVisible, setDrawerVisible] = useState(false)
  const [form] = Form.useForm()

  const widgetTypes = [
    { type: 'text', label: '文本显示', icon: '📝' },
    { type: 'number', label: '数值显示', icon: '🔢' },
    { type: 'chart', label: '图表', icon: '📊' },
    { type: 'gauge', label: '仪表盘', icon: '⏲️' },
    { type: 'button', label: '按钮', icon: '🔘' },
    { type: 'switch', label: '开关', icon: '🔄' },
  ]

  const addWidget = (type: Widget['type']) => {
    const newWidget: Widget = {
      id: Date.now().toString(),
      type,
      title: `新${widgetTypes.find(w => w.type === type)?.label}`,
      config: getDefaultConfig(type),
    }
    
    setWidgets([...widgets, newWidget])
    
    // 添加到布局
    const newLayout = {
      i: newWidget.id,
      x: (widgets.length * 2) % 12,
      y: Math.floor(widgets.length / 6) * 2,
      w: 2,
      h: 2,
    }
    
    setLayouts({
      ...layouts,
      lg: [...(layouts.lg || []), newLayout],
    })
  }

  const getDefaultConfig = (type: Widget['type']) => {
    switch (type) {
      case 'text':
        return { fontSize: 14, color: '#000000', backgroundColor: '#ffffff' }
      case 'number':
        return { fontSize: 24, color: '#1890ff', unit: '', precision: 2 }
      case 'chart':
        return { chartType: 'line', color: '#1890ff', showGrid: true }
      case 'gauge':
        return { min: 0, max: 100, color: '#52c41a', showValue: true }
      case 'button':
        return { text: '按钮', color: '#1890ff', action: 'send_command' }
      case 'switch':
        return { onText: '开', offText: '关', defaultValue: false }
      default:
        return {}
    }
  }

  const renderWidget = (widget: Widget) => {
    const { type, title, config } = widget
    
    switch (type) {
      case 'text':
        return (
          <div style={{ 
            padding: 16, 
            fontSize: config.fontSize,
            color: config.color,
            backgroundColor: config.backgroundColor,
            height: '100%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            {title}
          </div>
        )
      case 'number':
        return (
          <div style={{ 
            padding: 16, 
            textAlign: 'center',
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center'
          }}>
            <div style={{ fontSize: 12, color: '#666', marginBottom: 8 }}>{title}</div>
            <div style={{ 
              fontSize: config.fontSize, 
              color: config.color, 
              fontWeight: 'bold' 
            }}>
              {(Math.random() * 100).toFixed(config.precision)}{config.unit}
            </div>
          </div>
        )
      case 'chart':
        return (
          <div style={{ padding: 16, height: '100%' }}>
            <div style={{ fontSize: 12, marginBottom: 8 }}>{title}</div>
            <div style={{ 
              height: 'calc(100% - 24px)', 
              background: 'linear-gradient(45deg, #f0f0f0 25%, transparent 25%)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: '#999'
            }}>
              📊 图表预览
            </div>
          </div>
        )
      case 'gauge':
        return (
          <div style={{ 
            padding: 16, 
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <div style={{ fontSize: 12, marginBottom: 8 }}>{title}</div>
            <div style={{ 
              width: 80, 
              height: 80, 
              borderRadius: '50%',
              border: `4px solid ${config.color}`,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: 16,
              fontWeight: 'bold'
            }}>
              {Math.round(Math.random() * 100)}
            </div>
          </div>
        )
      case 'button':
        return (
          <div style={{ 
            padding: 16, 
            height: '100%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <Button 
              type="primary" 
              style={{ backgroundColor: config.color }}
              block
            >
              {config.text}
            </Button>
          </div>
        )
      case 'switch':
        return (
          <div style={{ 
            padding: 16, 
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <div style={{ fontSize: 12, marginBottom: 8 }}>{title}</div>
            <div style={{ 
              width: 60, 
              height: 30, 
              backgroundColor: config.defaultValue ? '#52c41a' : '#d9d9d9',
              borderRadius: 15,
              position: 'relative',
              cursor: 'pointer'
            }}>
              <div style={{
                width: 26,
                height: 26,
                backgroundColor: 'white',
                borderRadius: '50%',
                position: 'absolute',
                top: 2,
                left: config.defaultValue ? 32 : 2,
                transition: 'all 0.3s'
              }} />
            </div>
          </div>
        )
      default:
        return <div>未知控件</div>
    }
  }

  const handleWidgetClick = (widget: Widget) => {
    setSelectedWidget(widget)
    form.setFieldsValue({
      title: widget.title,
      dataBinding: widget.dataBinding,
      ...widget.config,
    })
    setDrawerVisible(true)
  }

  const handleConfigSave = () => {
    if (!selectedWidget) return
    
    form.validateFields().then(values => {
      const { title, dataBinding, ...config } = values
      
      setWidgets(widgets.map(w => 
        w.id === selectedWidget.id 
          ? { ...w, title, dataBinding, config }
          : w
      ))
      
      setDrawerVisible(false)
    })
  }

  const deleteWidget = (widgetId: string) => {
    setWidgets(widgets.filter(w => w.id !== widgetId))
    setLayouts({
      ...layouts,
      lg: layouts.lg?.filter((item: any) => item.i !== widgetId) || [],
    })
  }

  return (
    <div style={{ padding: '0 24px' }}>
      <Row gutter={16}>
        {/* 控件工具栏 */}
        <Col span={4}>
          <Card title="控件库" size="small">
            <Space direction="vertical" style={{ width: '100%' }}>
              {widgetTypes.map(widgetType => (
                <Button
                  key={widgetType.type}
                  block
                  onClick={() => addWidget(widgetType.type as Widget['type'])}
                  style={{ textAlign: 'left' }}
                >
                  {widgetType.icon} {widgetType.label}
                </Button>
              ))}
            </Space>
          </Card>
        </Col>
        
        {/* 设计画布 */}
        <Col span={20}>
          <Card 
            title="界面设计器" 
            extra={
              <Space>
                <Button>预览</Button>
                <Button type="primary">保存</Button>
              </Space>
            }
          >
            <div style={{ minHeight: 600, border: '1px dashed #d9d9d9', borderRadius: 6 }}>
              <ResponsiveGridLayout
                className="layout"
                layouts={layouts}
                onLayoutChange={(_, layouts) => setLayouts(layouts)}
                breakpoints={{ lg: 1200, md: 996, sm: 768, xs: 480, xxs: 0 }}
                cols={{ lg: 12, md: 10, sm: 6, xs: 4, xxs: 2 }}
                rowHeight={60}
                margin={[16, 16]}
              >
                {widgets.map(widget => (
                  <div key={widget.id} style={{ position: 'relative' }}>
                    <Card 
                      size="small" 
                      style={{ height: '100%', cursor: 'pointer' }}
                      onClick={() => handleWidgetClick(widget)}
                      bodyStyle={{ padding: 0, height: '100%' }}
                    >
                      {renderWidget(widget)}
                      <div style={{
                        position: 'absolute',
                        top: 4,
                        right: 4,
                        opacity: 0.7
                      }}>
                        <Space size={4}>
                          <Button 
                            size="small" 
                            type="text" 
                            icon={<SettingOutlined />}
                            onClick={(e) => {
                              e.stopPropagation()
                              handleWidgetClick(widget)
                            }}
                          />
                          <Button 
                            size="small" 
                            type="text" 
                            danger
                            icon={<DeleteOutlined />}
                            onClick={(e) => {
                              e.stopPropagation()
                              deleteWidget(widget.id)
                            }}
                          />
                        </Space>
                      </div>
                    </Card>
                  </div>
                ))}
              </ResponsiveGridLayout>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 属性配置抽屉 */}
      <Drawer
        title="控件属性"
        placement="right"
        width={400}
        open={drawerVisible}
        onClose={() => setDrawerVisible(false)}
        extra={
          <Space>
            <Button onClick={() => setDrawerVisible(false)}>取消</Button>
            <Button type="primary" onClick={handleConfigSave}>保存</Button>
          </Space>
        }
      >
        {selectedWidget && (
          <Form form={form} layout="vertical">
            <Form.Item label="控件标题" name="title">
              <Input />
            </Form.Item>
            
            <Form.Item label="数据绑定" name="dataBinding">
              <Select placeholder="选择数据源">
                <Select.Option value="temperature">温度</Select.Option>
                <Select.Option value="voltage">电压</Select.Option>
                <Select.Option value="current">电流</Select.Option>
              </Select>
            </Form.Item>

            {/* 根据控件类型显示不同的配置项 */}
            {selectedWidget.type === 'text' && (
              <>
                <Form.Item label="字体大小" name="fontSize">
                  <Input type="number" />
                </Form.Item>
                <Form.Item label="文字颜色" name="color">
                  <Input type="color" />
                </Form.Item>
                <Form.Item label="背景颜色" name="backgroundColor">
                  <Input type="color" />
                </Form.Item>
              </>
            )}

            {selectedWidget.type === 'number' && (
              <>
                <Form.Item label="字体大小" name="fontSize">
                  <Input type="number" />
                </Form.Item>
                <Form.Item label="数字颜色" name="color">
                  <Input type="color" />
                </Form.Item>
                <Form.Item label="单位" name="unit">
                  <Input placeholder="例如: °C" />
                </Form.Item>
                <Form.Item label="小数位数" name="precision">
                  <Input type="number" min={0} max={6} />
                </Form.Item>
              </>
            )}

            {selectedWidget.type === 'gauge' && (
              <>
                <Form.Item label="最小值" name="min">
                  <Input type="number" />
                </Form.Item>
                <Form.Item label="最大值" name="max">
                  <Input type="number" />
                </Form.Item>
                <Form.Item label="颜色" name="color">
                  <Input type="color" />
                </Form.Item>
              </>
            )}

            {selectedWidget.type === 'button' && (
              <>
                <Form.Item label="按钮文字" name="text">
                  <Input />
                </Form.Item>
                <Form.Item label="按钮颜色" name="color">
                  <Input type="color" />
                </Form.Item>
                <Form.Item label="点击动作" name="action">
                  <Select>
                    <Select.Option value="send_command">发送指令</Select.Option>
                    <Select.Option value="toggle_widget">切换控件</Select.Option>
                    <Select.Option value="custom">自定义</Select.Option>
                  </Select>
                </Form.Item>
              </>
            )}
          </Form>
        )}
      </Drawer>
    </div>
  )
}

export default InterfaceDesign