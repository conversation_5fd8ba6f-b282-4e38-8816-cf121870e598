import re
import json
from datetime import datetime


def bytes_to_hex(data: bytes, separator: str = " ") -> str:
    """将字节数据转换为十六进制字符串
    
    Args:
        data: 字节数据
        separator: 分隔符
        
    Returns:
        十六进制字符串
    """
    return separator.join(f"{byte:02X}" for byte in data)


def hex_to_bytes(hex_string: str) -> bytes:
    """将十六进制字符串转换为字节数据
    
    Args:
        hex_string: 十六进制字符串
        
    Returns:
        字节数据
    """
    # 移除空格和其他分隔符
    hex_string = re.sub(r'[^0-9A-Fa-f]', '', hex_string)
    
    # 确保长度为偶数
    if len(hex_string) % 2 != 0:
        hex_string = '0' + hex_string
    
    return bytes.fromhex(hex_string)


def string_to_bytes(text: str, encoding: str = 'utf-8') -> bytes:
    """将字符串转换为字节数据
    
    Args:
        text: 字符串
        encoding: 编码格式
        
    Returns:
        字节数据
    """
    return text.encode(encoding)


def bytes_to_string(data: bytes, encoding: str = 'utf-8') -> str:
    """将字节数据转换为字符串
    
    Args:
        data: 字节数据
        encoding: 编码格式
        
    Returns:
        字符串
    """
    return data.decode(encoding, errors='replace')


def int_to_bytes(value: int, length: int = None, byteorder: str = 'big') -> bytes:
    """将整数转换为字节数据
    
    Args:
        value: 整数值
        length: 字节长度
        byteorder: 字节序
        
    Returns:
        字节数据
    """
    if length is None:
        length = (value.bit_length() + 7) // 8
    return value.to_bytes(length, byteorder)


def bytes_to_int(data: bytes, byteorder: str = 'big') -> int:
    """将字节数据转换为整数
    
    Args:
        data: 字节数据
        byteorder: 字节序
        
    Returns:
        整数值
    """
    return int.from_bytes(data, byteorder)


def format_data_display(data: bytes, format_type: str = 'hex') -> str:
    """格式化数据显示
    
    Args:
        data: 字节数据
        format_type: 格式类型 ('hex', 'ascii', 'decimal')
        
    Returns:
        格式化后的字符串
    """
    if format_type == 'hex':
        return bytes_to_hex(data)
    elif format_type == 'ascii':
        return bytes_to_string(data)
    elif format_type == 'decimal':
        return ' '.join(str(byte) for byte in data)
    else:
        return str(data)


def parse_input_data(input_str: str, input_format: str = 'auto') -> bytes:
    """解析输入数据
    
    Args:
        input_str: 输入字符串
        input_format: 输入格式 ('auto', 'hex', 'ascii', 'decimal')
        
    Returns:
        字节数据
    """
    if input_format == 'hex' or (input_format == 'auto' and is_hex_string(input_str)):
        return hex_to_bytes(input_str)
    elif input_format == 'decimal':
        values = [int(x) for x in input_str.split()]
        return bytes(values)
    else:
        return string_to_bytes(input_str)


def is_hex_string(text: str) -> bool:
    """检查字符串是否为十六进制格式
    
    Args:
        text: 待检查的字符串
        
    Returns:
        是否为十六进制格式
    """
    # 移除空格和常见分隔符
    clean_text = re.sub(r'[\s:-]', '', text)
    return bool(re.match(r'^[0-9A-Fa-f]+$', clean_text))


def calculate_checksum(data: bytes, algorithm: str = 'sum') -> int:
    """计算校验和
    
    Args:
        data: 字节数据
        algorithm: 算法类型 ('sum', 'xor')
        
    Returns:
        校验和值
    """
    if algorithm == 'sum':
        return sum(data) & 0xFF
    elif algorithm == 'xor':
        result = 0
        for byte in data:
            result ^= byte
        return result
    else:
        raise ValueError(f"Unsupported algorithm: {algorithm}")


def calculate_crc8(data: bytes, polynomial: int = 0x07) -> int:
    """计算CRC8校验
    
    Args:
        data: 字节数据
        polynomial: 多项式
        
    Returns:
        CRC8值
    """
    crc = 0
    for byte in data:
        crc ^= byte
        for _ in range(8):
            if crc & 0x80:
                crc = (crc << 1) ^ polynomial
            else:
                crc <<= 1
            crc &= 0xFF
    return crc


def calculate_crc16(data: bytes, polynomial: int = 0x1021) -> int:
    """计算CRC16校验
    
    Args:
        data: 字节数据
        polynomial: 多项式
        
    Returns:
        CRC16值
    """
    crc = 0
    for byte in data:
        crc ^= byte << 8
        for _ in range(8):
            if crc & 0x8000:
                crc = (crc << 1) ^ polynomial
            else:
                crc <<= 1
            crc &= 0xFFFF
    return crc


def validate_data_format(data: str, expected_format: str) -> bool:
    """验证数据格式
    
    Args:
        data: 数据字符串
        expected_format: 期望格式
        
    Returns:
        是否符合格式
    """
    if expected_format == 'hex':
        return is_hex_string(data)
    elif expected_format == 'decimal':
        try:
            [int(x) for x in data.split()]
            return True
        except ValueError:
            return False
    elif expected_format == 'ascii':
        return True  # ASCII可以是任何字符串
    else:
        return False


def convert_between_formats(data: str, from_format: str, to_format: str) -> str:
    """在不同格式间转换数据
    
    Args:
        data: 输入数据
        from_format: 源格式
        to_format: 目标格式
        
    Returns:
        转换后的数据
    """
    # 先转换为字节数据
    bytes_data = parse_input_data(data, from_format)
    
    # 再转换为目标格式
    return format_data_display(bytes_data, to_format)


def parse_protocol_field(data: bytes, field_def: dict) -> any:
    """解析协议字段
    
    Args:
        data: 字节数据
        field_def: 字段定义
        
    Returns:
        解析后的值
    """
    field_type = field_def.get('type', 'bytes')
    length = field_def.get('length', len(data))
    byteorder = field_def.get('byteorder', 'big')
    
    if field_type == 'int':
        return bytes_to_int(data[:length], byteorder)
    elif field_type == 'string':
        encoding = field_def.get('encoding', 'utf-8')
        return bytes_to_string(data[:length], encoding)
    elif field_type == 'hex':
        return bytes_to_hex(data[:length])
    else:
        return data[:length]


def build_protocol_field(value: any, field_def: dict) -> bytes:
    """构建协议字段
    
    Args:
        value: 字段值
        field_def: 字段定义
        
    Returns:
        字节数据
    """
    field_type = field_def.get('type', 'bytes')
    length = field_def.get('length')
    byteorder = field_def.get('byteorder', 'big')
    
    if field_type == 'int':
        return int_to_bytes(value, length, byteorder)
    elif field_type == 'string':
        encoding = field_def.get('encoding', 'utf-8')
        data = string_to_bytes(str(value), encoding)
        if length and len(data) < length:
            data += b'\x00' * (length - len(data))  # 填充空字节
        return data[:length] if length else data
    elif field_type == 'hex':
        return hex_to_bytes(str(value))
    else:
        return bytes(value) if not isinstance(value, bytes) else value


def escape_string(text: str) -> str:
    """转义字符串中的特殊字符
    
    Args:
        text: 原始字符串
        
    Returns:
        转义后的字符串
    """
    escape_map = {
        '\\': '\\\\',
        '\n': '\\n',
        '\r': '\\r',
        '\t': '\\t',
        '"': '\\"',
        "'": "\\'"}
    
    result = text
    for char, escape_seq in escape_map.items():
        result = result.replace(char, escape_seq)
    
    return result


def unescape_string(text: str) -> str:
    """反转义字符串

    Args:
        text: 转义后的字符串

    Returns:
        原始字符串
    """
    unescape_map = {
        '\\\\': '\\',
        '\\n': '\n',
        '\\r': '\r',
        '\\t': '\t',
        '\\"': '"',
        "\\'": "'"
    }

    result = text
    for escape_seq, char in unescape_map.items():
        result = result.replace(escape_seq, char)

    return result
