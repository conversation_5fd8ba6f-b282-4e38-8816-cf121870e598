import json
import asyncio
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime
from enum import Enum
from loguru import logger

from backend.models.base import Flow, FlowNode, FlowEdge, FlowExecution
from backend.utils.database import get_db_manager
from backend.services.serial_service import SerialService
from backend.websocket_manager import WebSocketManager


class NodeType(str, Enum):
    """节点类型"""
    START = "start"
    END = "end"
    SERIAL_SEND = "serial_send"
    SERIAL_RECEIVE = "serial_receive"
    DELAY = "delay"
    CONDITION = "condition"
    LOOP = "loop"
    SCRIPT = "script"
    NOTIFICATION = "notification"


class ExecutionStatus(str, Enum):
    """执行状态"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    PAUSED = "paused"


class FlowService:
    """流程引擎服务"""
    
    def __init__(self, serial_service: SerialService, websocket_manager: WebSocketManager):
        self.db = get_db_manager()
        self.serial_service = serial_service
        self.websocket_manager = websocket_manager
        self.running_flows: Dict[str, Dict[str, Any]] = {}  # 运行中的流程
        self.node_templates = self._load_node_templates()
    
    def _load_node_templates(self) -> Dict[str, Dict[str, Any]]:
        """加载节点模板"""
        return {
            NodeType.START: {
                "name": "开始节点",
                "icon": "PlayCircleOutlined",
                "color": "#52c41a",
                "inputs": [],
                "outputs": ["default"],
                "configSchema": {
                    "description": {"type": "string", "label": "描述", "default": "流程开始"}
                }
            },
            NodeType.END: {
                "name": "结束节点",
                "icon": "StopOutlined",
                "color": "#ff4d4f",
                "inputs": ["default"],
                "outputs": [],
                "configSchema": {
                    "description": {"type": "string", "label": "描述", "default": "流程结束"}
                }
            },
            NodeType.SERIAL_SEND: {
                "name": "串口发送",
                "icon": "SendOutlined",
                "color": "#1890ff",
                "inputs": ["default"],
                "outputs": ["success", "error"],
                "configSchema": {
                    "data": {"type": "string", "label": "发送数据", "required": True},
                    "format": {"type": "select", "label": "数据格式", "options": ["ascii", "hex"], "default": "ascii"},
                    "timeout": {"type": "number", "label": "超时时间(ms)", "default": 5000, "min": 100}
                }
            },
            NodeType.SERIAL_RECEIVE: {
                "name": "串口接收",
                "icon": "InboxOutlined",
                "color": "#722ed1",
                "inputs": ["default"],
                "outputs": ["success", "timeout"],
                "configSchema": {
                    "timeout": {"type": "number", "label": "超时时间(ms)", "default": 5000, "min": 100},
                    "expected_data": {"type": "string", "label": "期望数据(可选)"},
                    "variable_name": {"type": "string", "label": "变量名", "default": "received_data"}
                }
            },
            NodeType.DELAY: {
                "name": "延时",
                "icon": "ClockCircleOutlined",
                "color": "#fa8c16",
                "inputs": ["default"],
                "outputs": ["default"],
                "configSchema": {
                    "duration": {"type": "number", "label": "延时时间(ms)", "required": True, "min": 1}
                }
            },
            NodeType.CONDITION: {
                "name": "条件判断",
                "icon": "BranchesOutlined",
                "color": "#eb2f96",
                "inputs": ["default"],
                "outputs": ["true", "false"],
                "configSchema": {
                    "condition": {"type": "string", "label": "条件表达式", "required": True},
                    "description": {"type": "string", "label": "描述"}
                }
            },
            NodeType.LOOP: {
                "name": "循环",
                "icon": "ReloadOutlined",
                "color": "#13c2c2",
                "inputs": ["default"],
                "outputs": ["loop", "exit"],
                "configSchema": {
                    "loop_type": {"type": "select", "label": "循环类型", "options": ["count", "condition"], "default": "count"},
                    "count": {"type": "number", "label": "循环次数", "min": 1},
                    "condition": {"type": "string", "label": "循环条件"}
                }
            },
            NodeType.SCRIPT: {
                "name": "脚本执行",
                "icon": "CodeOutlined",
                "color": "#595959",
                "inputs": ["default"],
                "outputs": ["success", "error"],
                "configSchema": {
                    "script": {"type": "textarea", "label": "Python脚本", "required": True},
                    "description": {"type": "string", "label": "描述"}
                }
            },
            NodeType.NOTIFICATION: {
                "name": "通知",
                "icon": "NotificationOutlined",
                "color": "#f759ab",
                "inputs": ["default"],
                "outputs": ["default"],
                "configSchema": {
                    "message": {"type": "string", "label": "通知消息", "required": True},
                    "type": {"type": "select", "label": "通知类型", "options": ["info", "success", "warning", "error"], "default": "info"}
                }
            }
        }
    
    async def get_flows(
        self, 
        page: int = 1, 
        page_size: int = 10,
        search: Optional[str] = None,
        project_id: Optional[str] = None,
        is_active: Optional[bool] = None
    ) -> Tuple[List[Flow], int]:
        """获取流程列表"""
        try:
            return await self.db.get_flows(
                page=page, page_size=page_size, search=search,
                project_id=project_id, is_active=is_active
            )
        except Exception as e:
            logger.error(f"获取流程列表失败: {e}")
            raise
    
    async def get_flow(self, flow_id: str) -> Optional[Flow]:
        """获取流程详情"""
        try:
            return await self.db.get_flow(flow_id)
        except Exception as e:
            logger.error(f"获取流程详情失败: {e}")
            raise
    
    async def create_flow(
        self,
        name: str,
        description: str = "",
        project_id: Optional[str] = None,
        nodes: List[FlowNode] = None,
        edges: List[FlowEdge] = None,
        config: Dict[str, Any] = None
    ) -> Flow:
        """创建流程"""
        try:
            flow_data = {
                "name": name,
                "description": description,
                "project_id": project_id,
                "nodes": nodes or [],
                "edges": edges or [],
                "config": config or {},
                "is_active": True,
                "created_at": datetime.now(),
                "updated_at": datetime.now()
            }
            
            return await self.db.create_flow(flow_data)
        except Exception as e:
            logger.error(f"创建流程失败: {e}")
            raise
    
    async def update_flow(
        self, 
        flow_id: str, 
        updates: Dict[str, Any]
    ) -> Optional[Flow]:
        """更新流程"""
        try:
            updates["updated_at"] = datetime.now()
            return await self.db.update_flow(flow_id, updates)
        except Exception as e:
            logger.error(f"更新流程失败: {e}")
            raise
    
    async def delete_flow(self, flow_id: str) -> bool:
        """删除流程"""
        try:
            # 检查是否有运行中的执行
            if flow_id in self.running_flows:
                await self.stop_execution(flow_id)
            
            return await self.db.delete_flow(flow_id)
        except Exception as e:
            logger.error(f"删除流程失败: {e}")
            raise
    
    async def execute_flow(
        self, 
        flow_id: str, 
        input_data: Dict[str, Any] = None
    ) -> FlowExecution:
        """执行流程"""
        try:
            flow = await self.get_flow(flow_id)
            if not flow:
                raise Exception(f"流程不存在: {flow_id}")
            
            # 验证流程
            validation_result = await self.validate_flow(flow_id)
            if not validation_result["valid"]:
                raise Exception(f"流程验证失败: {validation_result['errors']}")
            
            # 创建执行记录
            execution_data = {
                "flow_id": flow_id,
                "status": ExecutionStatus.PENDING,
                "input_data": input_data or {},
                "output_data": {},
                "variables": {},
                "current_node": None,
                "start_time": datetime.now(),
                "end_time": None,
                "logs": []
            }
            
            execution = await self.db.create_execution(execution_data)
            
            # 启动执行任务
            asyncio.create_task(self._execute_flow_async(execution.id, flow))
            
            return execution
            
        except Exception as e:
            logger.error(f"执行流程失败: {e}")
            raise
    
    async def _execute_flow_async(self, execution_id: str, flow: Flow):
        """异步执行流程"""
        try:
            # 更新执行状态
            await self.db.update_execution(execution_id, {
                "status": ExecutionStatus.RUNNING
            })
            
            # 添加到运行中的流程
            self.running_flows[execution_id] = {
                "flow": flow,
                "status": ExecutionStatus.RUNNING,
                "variables": {},
                "current_node": None,
                "cancelled": False,
                "paused": False
            }
            
            # 发送状态更新
            await self.websocket_manager.send_flow_status({
                "execution_id": execution_id,
                "flow_id": flow.id,
                "status": ExecutionStatus.RUNNING,
                "message": "流程开始执行"
            })
            
            # 查找开始节点
            start_node = None
            for node in flow.nodes:
                if node.type == NodeType.START:
                    start_node = node
                    break
            
            if not start_node:
                raise Exception("未找到开始节点")
            
            # 执行流程
            result = await self._execute_node(execution_id, start_node, flow)
            
            # 更新执行结果
            status = ExecutionStatus.COMPLETED if result["success"] else ExecutionStatus.FAILED
            await self.db.update_execution(execution_id, {
                "status": status,
                "output_data": result.get("output_data", {}),
                "end_time": datetime.now()
            })
            
            # 发送完成通知
            await self.websocket_manager.send_flow_status({
                "execution_id": execution_id,
                "flow_id": flow.id,
                "status": status,
                "message": "流程执行完成" if result["success"] else f"流程执行失败: {result.get('error', '')}"
            })
            
        except Exception as e:
            logger.error(f"流程执行异常: {e}")
            
            # 更新执行状态为失败
            await self.db.update_execution(execution_id, {
                "status": ExecutionStatus.FAILED,
                "end_time": datetime.now()
            })
            
            # 发送错误通知
            await self.websocket_manager.send_flow_status({
                "execution_id": execution_id,
                "flow_id": flow.id,
                "status": ExecutionStatus.FAILED,
                "message": f"流程执行异常: {str(e)}"
            })
            
        finally:
            # 从运行中的流程移除
            if execution_id in self.running_flows:
                del self.running_flows[execution_id]
    
    async def _execute_node(
        self, 
        execution_id: str, 
        node: FlowNode, 
        flow: Flow
    ) -> Dict[str, Any]:
        """执行节点"""
        try:
            # 检查是否被取消或暂停
            if execution_id in self.running_flows:
                flow_state = self.running_flows[execution_id]
                if flow_state["cancelled"]:
                    return {"success": False, "error": "流程已取消"}
                
                while flow_state["paused"]:
                    await asyncio.sleep(0.1)
                    if flow_state["cancelled"]:
                        return {"success": False, "error": "流程已取消"}
            
            # 更新当前节点
            self.running_flows[execution_id]["current_node"] = node.id
            await self.db.update_execution(execution_id, {
                "current_node": node.id
            })
            
            # 记录日志
            await self._add_execution_log(execution_id, f"开始执行节点: {node.name} ({node.type})")
            
            # 根据节点类型执行
            if node.type == NodeType.START:
                result = {"success": True, "output": "default"}
            elif node.type == NodeType.END:
                result = {"success": True, "output": None}
            elif node.type == NodeType.SERIAL_SEND:
                result = await self._execute_serial_send_node(execution_id, node)
            elif node.type == NodeType.SERIAL_RECEIVE:
                result = await self._execute_serial_receive_node(execution_id, node)
            elif node.type == NodeType.DELAY:
                result = await self._execute_delay_node(execution_id, node)
            elif node.type == NodeType.CONDITION:
                result = await self._execute_condition_node(execution_id, node)
            elif node.type == NodeType.LOOP:
                result = await self._execute_loop_node(execution_id, node, flow)
            elif node.type == NodeType.SCRIPT:
                result = await self._execute_script_node(execution_id, node)
            elif node.type == NodeType.NOTIFICATION:
                result = await self._execute_notification_node(execution_id, node)
            else:
                result = {"success": False, "error": f"未知节点类型: {node.type}"}
            
            if not result["success"]:
                await self._add_execution_log(execution_id, f"节点执行失败: {result.get('error', '')}")
                return result
            
            # 如果是结束节点，直接返回
            if node.type == NodeType.END:
                await self._add_execution_log(execution_id, "流程执行完成")
                return result
            
            # 查找下一个节点
            output_port = result.get("output", "default")
            next_node = self._find_next_node(node, output_port, flow)
            
            if not next_node:
                await self._add_execution_log(execution_id, f"未找到下一个节点 (输出端口: {output_port})")
                return {"success": False, "error": "未找到下一个节点"}
            
            # 递归执行下一个节点
            return await self._execute_node(execution_id, next_node, flow)
            
        except Exception as e:
            logger.error(f"节点执行异常: {e}")
            await self._add_execution_log(execution_id, f"节点执行异常: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def _find_next_node(self, current_node: FlowNode, output_port: str, flow: Flow) -> Optional[FlowNode]:
        """查找下一个节点"""
        for edge in flow.edges:
            if edge.source == current_node.id and edge.source_handle == output_port:
                for node in flow.nodes:
                    if node.id == edge.target:
                        return node
        return None
    
    async def _execute_serial_send_node(self, execution_id: str, node: FlowNode) -> Dict[str, Any]:
        """执行串口发送节点"""
        try:
            config = node.config or {}
            data = config.get("data", "")
            format_type = config.get("format", "ascii")
            timeout = config.get("timeout", 5000)
            
            # 替换变量
            variables = self.running_flows[execution_id]["variables"]
            data = self._replace_variables(data, variables)
            
            # 发送数据
            success = await self.serial_service.send_data(data, format_type)
            
            if success:
                await self._add_execution_log(execution_id, f"串口发送成功: {data}")
                return {"success": True, "output": "success"}
            else:
                await self._add_execution_log(execution_id, f"串口发送失败: {data}")
                return {"success": True, "output": "error"}
                
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def _execute_serial_receive_node(self, execution_id: str, node: FlowNode) -> Dict[str, Any]:
        """执行串口接收节点"""
        try:
            config = node.config or {}
            timeout = config.get("timeout", 5000) / 1000  # 转换为秒
            expected_data = config.get("expected_data", "")
            variable_name = config.get("variable_name", "received_data")
            
            # 等待接收数据
            start_time = datetime.now()
            received_data = None
            
            while (datetime.now() - start_time).total_seconds() < timeout:
                # 这里应该从串口服务获取最新数据
                # 简化实现，实际应该监听串口数据
                await asyncio.sleep(0.1)
                
                # 检查是否收到期望数据
                if expected_data and received_data and expected_data in received_data:
                    break
            
            if received_data:
                # 保存到变量
                self.running_flows[execution_id]["variables"][variable_name] = received_data
                await self._add_execution_log(execution_id, f"串口接收成功: {received_data}")
                return {"success": True, "output": "success"}
            else:
                await self._add_execution_log(execution_id, "串口接收超时")
                return {"success": True, "output": "timeout"}
                
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def _execute_delay_node(self, execution_id: str, node: FlowNode) -> Dict[str, Any]:
        """执行延时节点"""
        try:
            config = node.config or {}
            duration = config.get("duration", 1000) / 1000  # 转换为秒
            
            await self._add_execution_log(execution_id, f"开始延时 {duration} 秒")
            await asyncio.sleep(duration)
            await self._add_execution_log(execution_id, "延时完成")
            
            return {"success": True, "output": "default"}
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def _execute_condition_node(self, execution_id: str, node: FlowNode) -> Dict[str, Any]:
        """执行条件判断节点"""
        try:
            config = node.config or {}
            condition = config.get("condition", "")
            
            # 替换变量
            variables = self.running_flows[execution_id]["variables"]
            condition = self._replace_variables(condition, variables)
            
            # 简单的条件判断（实际应该使用安全的表达式求值）
            try:
                result = eval(condition, {"__builtins__": {}}, variables)
                output = "true" if result else "false"
                
                await self._add_execution_log(execution_id, f"条件判断: {condition} = {result}")
                return {"success": True, "output": output}
                
            except Exception as eval_error:
                await self._add_execution_log(execution_id, f"条件表达式错误: {eval_error}")
                return {"success": False, "error": f"条件表达式错误: {eval_error}"}
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def _execute_loop_node(self, execution_id: str, node: FlowNode, flow: Flow) -> Dict[str, Any]:
        """执行循环节点"""
        try:
            config = node.config or {}
            loop_type = config.get("loop_type", "count")
            
            if loop_type == "count":
                count = config.get("count", 1)
                for i in range(count):
                    # 设置循环变量
                    self.running_flows[execution_id]["variables"]["loop_index"] = i
                    
                    # 查找循环体的第一个节点
                    loop_node = self._find_next_node(node, "loop", flow)
                    if loop_node:
                        result = await self._execute_node(execution_id, loop_node, flow)
                        if not result["success"]:
                            return result
                
                return {"success": True, "output": "exit"}
            else:
                # 条件循环（简化实现）
                return {"success": True, "output": "exit"}
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def _execute_script_node(self, execution_id: str, node: FlowNode) -> Dict[str, Any]:
        """执行脚本节点"""
        try:
            config = node.config or {}
            script = config.get("script", "")
            
            # 准备执行环境
            variables = self.running_flows[execution_id]["variables"]
            
            # 执行脚本（注意：这里应该使用更安全的执行环境）
            try:
                exec(script, {"__builtins__": {}}, variables)
                await self._add_execution_log(execution_id, "脚本执行成功")
                return {"success": True, "output": "success"}
                
            except Exception as script_error:
                await self._add_execution_log(execution_id, f"脚本执行错误: {script_error}")
                return {"success": True, "output": "error"}
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def _execute_notification_node(self, execution_id: str, node: FlowNode) -> Dict[str, Any]:
        """执行通知节点"""
        try:
            config = node.config or {}
            message = config.get("message", "")
            notification_type = config.get("type", "info")
            
            # 替换变量
            variables = self.running_flows[execution_id]["variables"]
            message = self._replace_variables(message, variables)
            
            # 发送通知
            await self.websocket_manager.send_notification({
                "type": notification_type,
                "message": message,
                "source": "flow_execution",
                "execution_id": execution_id
            })
            
            await self._add_execution_log(execution_id, f"发送通知: {message}")
            return {"success": True, "output": "default"}
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _replace_variables(self, text: str, variables: Dict[str, Any]) -> str:
        """替换文本中的变量"""
        for key, value in variables.items():
            text = text.replace(f"${{{key}}}", str(value))
        return text
    
    async def _add_execution_log(self, execution_id: str, message: str):
        """添加执行日志"""
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "message": message
        }
        
        # 获取当前日志
        execution = await self.db.get_execution(execution_id)
        if execution:
            logs = execution.logs or []
            logs.append(log_entry)
            
            await self.db.update_execution(execution_id, {"logs": logs})
    
    async def pause_execution(self, execution_id: str) -> bool:
        """暂停执行"""
        try:
            if execution_id in self.running_flows:
                self.running_flows[execution_id]["paused"] = True
                
                await self.db.update_execution(execution_id, {
                    "status": ExecutionStatus.PAUSED
                })
                
                await self.websocket_manager.send_flow_status({
                    "execution_id": execution_id,
                    "status": ExecutionStatus.PAUSED,
                    "message": "流程已暂停"
                })
                
                return True
            return False
            
        except Exception as e:
            logger.error(f"暂停执行失败: {e}")
            return False
    
    async def resume_execution(self, execution_id: str) -> bool:
        """恢复执行"""
        try:
            if execution_id in self.running_flows:
                self.running_flows[execution_id]["paused"] = False
                
                await self.db.update_execution(execution_id, {
                    "status": ExecutionStatus.RUNNING
                })
                
                await self.websocket_manager.send_flow_status({
                    "execution_id": execution_id,
                    "status": ExecutionStatus.RUNNING,
                    "message": "流程已恢复"
                })
                
                return True
            return False
            
        except Exception as e:
            logger.error(f"恢复执行失败: {e}")
            return False
    
    async def stop_execution(self, execution_id: str) -> bool:
        """停止执行"""
        try:
            if execution_id in self.running_flows:
                self.running_flows[execution_id]["cancelled"] = True
                
                await self.db.update_execution(execution_id, {
                    "status": ExecutionStatus.CANCELLED,
                    "end_time": datetime.now()
                })
                
                await self.websocket_manager.send_flow_status({
                    "execution_id": execution_id,
                    "status": ExecutionStatus.CANCELLED,
                    "message": "流程已取消"
                })
                
                return True
            return False
            
        except Exception as e:
            logger.error(f"停止执行失败: {e}")
            return False
    
    async def get_executions(
        self, 
        flow_id: Optional[str] = None,
        page: int = 1, 
        page_size: int = 10
    ) -> Tuple[List[FlowExecution], int]:
        """获取执行历史"""
        try:
            return await self.db.get_executions(
                flow_id=flow_id, page=page, page_size=page_size
            )
        except Exception as e:
            logger.error(f"获取执行历史失败: {e}")
            raise
    
    async def get_execution(self, execution_id: str) -> Optional[FlowExecution]:
        """获取执行详情"""
        try:
            return await self.db.get_execution(execution_id)
        except Exception as e:
            logger.error(f"获取执行详情失败: {e}")
            raise
    
    async def get_execution_logs(
        self, 
        execution_id: str
    ) -> List[Dict[str, Any]]:
        """获取执行日志"""
        try:
            execution = await self.get_execution(execution_id)
            return execution.logs if execution else []
        except Exception as e:
            logger.error(f"获取执行日志失败: {e}")
            raise
    
    async def validate_flow(self, flow_id: str) -> Dict[str, Any]:
        """验证流程"""
        try:
            flow = await self.get_flow(flow_id)
            if not flow:
                return {"valid": False, "errors": ["流程不存在"]}
            
            errors = []
            warnings = []
            
            # 检查基本信息
            if not flow.name.strip():
                errors.append("流程名称不能为空")
            
            # 检查节点
            if not flow.nodes:
                errors.append("流程没有节点")
            else:
                start_nodes = [n for n in flow.nodes if n.type == NodeType.START]
                end_nodes = [n for n in flow.nodes if n.type == NodeType.END]
                
                if len(start_nodes) == 0:
                    errors.append("流程缺少开始节点")
                elif len(start_nodes) > 1:
                    errors.append("流程只能有一个开始节点")
                
                if len(end_nodes) == 0:
                    warnings.append("流程缺少结束节点")
                
                # 检查节点配置
                for node in flow.nodes:
                    if node.type in self.node_templates:
                        template = self.node_templates[node.type]
                        schema = template.get("configSchema", {})
                        
                        for field, field_config in schema.items():
                            if field_config.get("required") and not node.config.get(field):
                                errors.append(f"节点 {node.name} 缺少必需配置: {field}")
            
            # 检查连接
            if flow.edges:
                node_ids = {n.id for n in flow.nodes}
                for edge in flow.edges:
                    if edge.source not in node_ids:
                        errors.append(f"连接的源节点不存在: {edge.source}")
                    if edge.target not in node_ids:
                        errors.append(f"连接的目标节点不存在: {edge.target}")
            
            # 检查连通性
            if flow.nodes and flow.edges:
                reachable_nodes = self._check_reachability(flow)
                unreachable_nodes = [n.name for n in flow.nodes if n.id not in reachable_nodes]
                if unreachable_nodes:
                    warnings.append(f"以下节点不可达: {', '.join(unreachable_nodes)}")
            
            return {
                "valid": len(errors) == 0,
                "errors": errors,
                "warnings": warnings,
                "node_count": len(flow.nodes),
                "edge_count": len(flow.edges)
            }
            
        except Exception as e:
            logger.error(f"流程验证失败: {e}")
            return {
                "valid": False,
                "errors": [f"验证过程出错: {str(e)}"]
            }
    
    def _check_reachability(self, flow: Flow) -> set:
        """检查节点可达性"""
        # 从开始节点开始进行深度优先搜索
        start_nodes = [n for n in flow.nodes if n.type == NodeType.START]
        if not start_nodes:
            return set()
        
        visited = set()
        stack = [start_nodes[0].id]
        
        while stack:
            current = stack.pop()
            if current in visited:
                continue
            
            visited.add(current)
            
            # 查找连接的下一个节点
            for edge in flow.edges:
                if edge.source == current and edge.target not in visited:
                    stack.append(edge.target)
        
        return visited
    
    async def duplicate_flow(
        self, 
        flow_id: str, 
        new_name: str
    ) -> Optional[Flow]:
        """复制流程"""
        try:
            original = await self.get_flow(flow_id)
            if not original:
                return None
            
            # 创建副本
            flow_data = {
                "name": new_name,
                "description": f"{original.description} (副本)",
                "project_id": original.project_id,
                "nodes": original.nodes,
                "edges": original.edges,
                "config": original.config,
                "is_active": True,
                "created_at": datetime.now(),
                "updated_at": datetime.now()
            }
            
            return await self.db.create_flow(flow_data)
            
        except Exception as e:
            logger.error(f"复制流程失败: {e}")
            raise
    
    async def export_flow(
        self, 
        flow_id: str, 
        format: str = "json"
    ) -> Optional[Dict[str, Any]]:
        """导出流程"""
        try:
            flow = await self.get_flow(flow_id)
            if not flow:
                return None
            
            export_data = {
                "name": flow.name,
                "description": flow.description,
                "nodes": [node.dict() for node in flow.nodes],
                "edges": [edge.dict() for edge in flow.edges],
                "config": flow.config,
                "export_time": datetime.now().isoformat(),
                "version": "1.0"
            }
            
            if format == "json":
                return {
                    "content": json.dumps(export_data, indent=2, ensure_ascii=False),
                    "filename": f"{flow.name}_flow.json",
                    "content_type": "application/json"
                }
            else:
                raise Exception(f"不支持的导出格式: {format}")
            
        except Exception as e:
            logger.error(f"导出流程失败: {e}")
            raise
    
    async def import_flow(
        self, 
        data: str, 
        format: str = "json",
        project_id: Optional[str] = None
    ) -> Flow:
        """导入流程"""
        try:
            if format == "json":
                import_data = json.loads(data)
            else:
                raise Exception(f"不支持的导入格式: {format}")
            
            # 验证导入数据
            required_fields = ["name", "nodes", "edges"]
            for field in required_fields:
                if field not in import_data:
                    raise Exception(f"缺少必需字段: {field}")
            
            # 转换节点和边数据
            nodes = [FlowNode(**node_data) for node_data in import_data["nodes"]]
            edges = [FlowEdge(**edge_data) for edge_data in import_data["edges"]]
            
            # 创建流程
            return await self.create_flow(
                name=import_data["name"],
                description=import_data.get("description", ""),
                project_id=project_id,
                nodes=nodes,
                edges=edges,
                config=import_data.get("config", {})
            )
            
        except Exception as e:
            logger.error(f"导入流程失败: {e}")
            raise
    
    async def get_node_templates(self) -> Dict[str, Dict[str, Any]]:
        """获取节点模板"""
        return self.node_templates
    
    async def get_running_flows(self) -> List[Dict[str, Any]]:
        """获取运行中的流程"""
        running_list = []
        for execution_id, flow_state in self.running_flows.items():
            running_list.append({
                "execution_id": execution_id,
                "flow_id": flow_state["flow"].id,
                "flow_name": flow_state["flow"].name,
                "status": flow_state["status"],
                "current_node": flow_state["current_node"]
            })
        return running_list
    
    async def get_flow_statistics(self) -> Dict[str, Any]:
        """获取流程统计信息"""
        try:
            return await self.db.get_flow_statistics()
        except Exception as e:
            logger.error(f"获取流程统计失败: {e}")
            raise