# 应用程序配置示例文件
# 复制此文件为 .env 并根据需要修改配置

# 应用程序配置
APP_NAME=串口通信测试工具
VERSION=1.0.0
ENVIRONMENT=development
DEBUG=true

# 服务器配置
HOST=127.0.0.1
PORT=8000

# 数据库配置
DATABASE_URL=sqlite:///./data/app.db
DATABASE_ECHO=false

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/app.log
LOG_MAX_SIZE=10 MB
LOG_BACKUP_COUNT=7

# CORS配置
ALLOWED_ORIGINS=["*"]

# 串口配置
SERIAL_TIMEOUT=1.0
SERIAL_MAX_HISTORY=10000
SERIAL_BUFFER_SIZE=4096

# WebSocket配置
WS_HEARTBEAT_INTERVAL=30
WS_MAX_CONNECTIONS=100

# 文件上传配置
UPLOAD_MAX_SIZE=10485760
UPLOAD_ALLOWED_EXTENSIONS=[".json", ".xml", ".csv", ".txt"]
UPLOAD_DIR=uploads

# 安全配置
SECRET_KEY=your-secret-key-change-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 备份配置
BACKUP_ENABLED=true
BACKUP_INTERVAL_HOURS=24
BACKUP_KEEP_DAYS=30
BACKUP_DIR=backups

# 性能配置
MAX_CONCURRENT_FLOWS=10
FLOW_EXECUTION_TIMEOUT=300