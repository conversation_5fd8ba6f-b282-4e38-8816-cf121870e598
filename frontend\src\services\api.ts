import axios from 'axios'

// 创建axios实例
const api = axios.create({
  baseURL: '/api', // 使用相对路径，会被vite.config.ts中的代理转发到后端
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 可以在这里添加认证token等
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response.data
  },
  (error) => {
    // 统一处理错误
    console.error('API请求错误:', error)
    return Promise.reject(error)
  }
)

// 协议相关API
export const protocolApi = {
  // 获取所有协议
  getProtocols: () => api.get('/protocols'),
  
  // 获取单个协议详情
  getProtocol: (id: string) => api.get(`/protocols/${id}`),
  
  // 创建新协议
  createProtocol: (data: any) => api.post('/protocols', data),
  
  // 更新协议
  updateProtocol: (id: string, data: any) => api.put(`/protocols/${id}`, data),
  
  // 删除协议
  deleteProtocol: (id: string) => api.delete(`/protocols/${id}`),
  
  // 测试协议解析
  testProtocol: (id: string, data: string) => api.post(`/protocols/${id}/test`, { data }),
}

// 界面设计相关API
export const interfaceApi = {
  // 获取所有界面
  getInterfaces: () => api.get('/interfaces'),
  
  // 获取单个界面详情
  getInterface: (id: string) => api.get(`/interfaces/${id}`),
  
  // 创建新界面
  createInterface: (data: any) => api.post('/interfaces', data),
  
  // 更新界面
  updateInterface: (id: string, data: any) => api.put(`/interfaces/${id}`, data),
  
  // 删除界面
  deleteInterface: (id: string) => api.delete(`/interfaces/${id}`),
}

// 流程引擎相关API
export const flowApi = {
  // 获取所有流程
  getFlows: () => api.get('/flows'),
  
  // 获取单个流程详情
  getFlow: (id: string) => api.get(`/flows/${id}`),
  
  // 创建新流程
  createFlow: (data: any) => api.post('/flows', data),
  
  // 更新流程
  updateFlow: (id: string, data: any) => api.put(`/flows/${id}`, data),
  
  // 删除流程
  deleteFlow: (id: string) => api.delete(`/flows/${id}`),
  
  // 运行流程
  runFlow: (id: string) => api.post(`/flows/${id}/run`),
  
  // 停止流程
  stopFlow: (id: string) => api.post(`/flows/${id}/stop`),
}

// 串口通信相关API
export const serialApi = {
  // 获取可用串口列表
  getPorts: () => api.get('/serial/ports'),
  
  // 连接串口
  connect: (port: string, config: any) => api.post('/serial/connect', { port, config }),
  
  // 断开串口连接
  disconnect: () => api.post('/serial/disconnect'),
  
  // 发送数据
  sendData: (data: string, format: 'hex' | 'ascii' = 'ascii') => 
    api.post('/serial/send', { data, format }),
  
  // 获取串口状态
  getStatus: () => api.get('/serial/status'),
}

// 项目管理相关API
export const projectApi = {
  // 获取所有项目
  getProjects: () => api.get('/projects'),
  
  // 获取单个项目详情
  getProject: (id: string) => api.get(`/projects/${id}`),
  
  // 创建新项目
  createProject: (data: any) => api.post('/projects', data),
  
  // 更新项目
  updateProject: (id: string, data: any) => api.put(`/projects/${id}`, data),
  
  // 删除项目
  deleteProject: (id: string) => api.delete(`/projects/${id}`),
  
  // 导出项目
  exportProject: (id: string) => api.get(`/projects/${id}/export`, { responseType: 'blob' }),
  
  // 导入项目
  importProject: (file: File) => {
    const formData = new FormData()
    formData.append('file', file)
    return api.post('/projects/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  },
  
  // 获取模板列表
  getTemplates: () => api.get('/templates'),
}

export default {
  protocolApi,
  interfaceApi,
  flowApi,
  serialApi,
  projectApi,
}