import { create } from 'zustand'
import { devtools } from 'zustand/middleware'

export interface SerialPort {
  path: string
  manufacturer?: string
  serialNumber?: string
  pnpId?: string
  locationId?: string
  productId?: string
  vendorId?: string
}

export interface SerialConfig {
  baudRate: number
  dataBits: 5 | 6 | 7 | 8
  stopBits: 1 | 2
  parity: 'none' | 'even' | 'odd' | 'mark' | 'space'
}

export interface SerialData {
  timestamp: string
  raw: string
  parsed?: Record<string, any>
}

interface SerialState {
  // 连接状态
  isConnected: boolean
  isConnecting: boolean
  
  // 串口信息
  availablePorts: SerialPort[]
  selectedPort: string | null
  config: SerialConfig
  
  // 数据
  receivedData: SerialData[]
  lastData: SerialData | null
  
  // WebSocket连接
  ws: WebSocket | null
  
  // Actions
  setConnected: (connected: boolean) => void
  setConnecting: (connecting: boolean) => void
  setAvailablePorts: (ports: SerialPort[]) => void
  setSelectedPort: (port: string | null) => void
  setConfig: (config: Partial<SerialConfig>) => void
  addReceivedData: (data: SerialData) => void
  clearReceivedData: () => void
  connectWebSocket: () => void
  disconnectWebSocket: () => void
  connectSerial: () => Promise<boolean>
  disconnectSerial: () => Promise<boolean>
  sendData: (data: string) => Promise<boolean>
  refreshPorts: () => Promise<void>
}

export const useSerialStore = create<SerialState>()(
  devtools(
    (set, get) => ({
      // Initial state
      isConnected: false,
      isConnecting: false,
      availablePorts: [],
      selectedPort: null,
      config: {
        baudRate: 9600,
        dataBits: 8,
        stopBits: 1,
        parity: 'none',
      },
      receivedData: [],
      lastData: null,
      ws: null,

      // Actions
      setConnected: (connected) => set({ isConnected: connected }),
      setConnecting: (connecting) => set({ isConnecting: connecting }),
      setAvailablePorts: (ports) => set({ availablePorts: ports }),
      setSelectedPort: (port) => set({ selectedPort: port }),
      setConfig: (config) => set((state) => ({ config: { ...state.config, ...config } })),
      
      addReceivedData: (data) => set((state) => ({
        receivedData: [...state.receivedData.slice(-999), data], // 保留最近1000条
        lastData: data,
      })),
      
      clearReceivedData: () => set({ receivedData: [], lastData: null }),

      connectWebSocket: () => {
        const { ws } = get()
        if (ws?.readyState === WebSocket.OPEN) return

        const newWs = new WebSocket('ws://localhost:8000/ws')
        
        newWs.onopen = () => {
          console.log('WebSocket连接已建立')
        }
        
        newWs.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data)
            if (data.type === 'serial_data') {
              get().addReceivedData({
                timestamp: data.timestamp,
                raw: data.raw,
                parsed: data.parsed,
              })
            } else if (data.type === 'connection_status') {
              get().setConnected(data.connected)
            }
          } catch (error) {
            console.error('解析WebSocket消息失败:', error)
          }
        }
        
        newWs.onclose = () => {
          console.log('WebSocket连接已关闭')
          set({ ws: null })
        }
        
        newWs.onerror = (error) => {
          console.error('WebSocket错误:', error)
        }
        
        set({ ws: newWs })
      },

      disconnectWebSocket: () => {
        const { ws } = get()
        if (ws) {
          ws.close()
          set({ ws: null })
        }
      },

      connectSerial: async () => {
        const { selectedPort, config } = get()
        if (!selectedPort) return false

        try {
          set({ isConnecting: true })
          const response = await fetch('/api/serial/connect', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ port: selectedPort, config }),
          })
          
          const result = await response.json()
          if (result.success) {
            set({ isConnected: true })
            return true
          }
          return false
        } catch (error) {
          console.error('连接串口失败:', error)
          return false
        } finally {
          set({ isConnecting: false })
        }
      },

      disconnectSerial: async () => {
        try {
          const response = await fetch('/api/serial/disconnect', {
            method: 'POST',
          })
          
          const result = await response.json()
          if (result.success) {
            set({ isConnected: false })
            return true
          }
          return false
        } catch (error) {
          console.error('断开串口失败:', error)
          return false
        }
      },

      sendData: async (data: string) => {
        try {
          const response = await fetch('/api/serial/send', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ data }),
          })
          
          const result = await response.json()
          return result.success
        } catch (error) {
          console.error('发送数据失败:', error)
          return false
        }
      },

      refreshPorts: async () => {
        try {
          const response = await fetch('/api/serial/ports');
          const ports = await response.json();
          set({ availablePorts: ports });
        } catch (error) {
          console.error('获取串口列表失败:', error);
        }
      },
    }),
    {
      name: 'serial-store',
    }
  )
);