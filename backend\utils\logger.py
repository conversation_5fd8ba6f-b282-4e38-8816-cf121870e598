import os
import sys
from pathlib import Path
from loguru import logger
from typing import Optional


def setup_logger(
    log_level: str = "INFO",
    log_file: Optional[str] = None,
    log_rotation: str = "10 MB",
    log_retention: str = "30 days",
    log_format: Optional[str] = None
) -> None:
    """配置日志系统
    
    Args:
        log_level: 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: 日志文件路径，如果为None则只输出到控制台
        log_rotation: 日志轮转大小
        log_retention: 日志保留时间
        log_format: 自定义日志格式
    """
    
    # 移除默认的处理器
    logger.remove()
    
    # 默认日志格式
    if log_format is None:
        log_format = (
            "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
            "<level>{level: <8}</level> | "
            "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
            "<level>{message}</level>"
        )
    
    # 添加控制台处理器
    logger.add(
        sys.stdout,
        format=log_format,
        level=log_level,
        colorize=True,
        backtrace=True,
        diagnose=True
    )
    
    # 添加文件处理器（如果指定了日志文件）
    if log_file:
        # 确保日志目录存在
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 添加文件处理器
        logger.add(
            log_file,
            format=log_format,
            level=log_level,
            rotation=log_rotation,
            retention=log_retention,
            compression="zip",
            backtrace=True,
            diagnose=True,
            encoding="utf-8"
        )
    
    # 设置第三方库的日志级别
    import logging
    
    # 设置uvicorn日志级别
    logging.getLogger("uvicorn").setLevel(logging.WARNING)
    logging.getLogger("uvicorn.access").setLevel(logging.WARNING)
    
    # 设置其他库的日志级别
    logging.getLogger("asyncio").setLevel(logging.WARNING)
    logging.getLogger("websockets").setLevel(logging.WARNING)
    
    logger.info(f"日志系统已配置 - 级别: {log_level}, 文件: {log_file or '仅控制台'}")


def get_logger(name: str):
    """获取指定名称的日志器
    
    Args:
        name: 日志器名称
        
    Returns:
        配置好的日志器实例
    """
    return logger.bind(name=name)


def log_function_call(func):
    """装饰器：记录函数调用
    
    Args:
        func: 要装饰的函数
        
    Returns:
        装饰后的函数
    """
    def wrapper(*args, **kwargs):
        func_name = func.__name__
        logger.debug(f"调用函数: {func_name}, 参数: args={args}, kwargs={kwargs}")
        
        try:
            result = func(*args, **kwargs)
            logger.debug(f"函数 {func_name} 执行成功")
            return result
        except Exception as e:
            logger.error(f"函数 {func_name} 执行失败: {e}")
            raise
    
    return wrapper


def log_async_function_call(func):
    """装饰器：记录异步函数调用
    
    Args:
        func: 要装饰的异步函数
        
    Returns:
        装饰后的异步函数
    """
    async def wrapper(*args, **kwargs):
        func_name = func.__name__
        logger.debug(f"调用异步函数: {func_name}, 参数: args={args}, kwargs={kwargs}")
        
        try:
            result = await func(*args, **kwargs)
            logger.debug(f"异步函数 {func_name} 执行成功")
            return result
        except Exception as e:
            logger.error(f"异步函数 {func_name} 执行失败: {e}")
            raise
    
    return wrapper


class LoggerMixin:
    """日志混入类，为类提供日志功能"""
    
    @property
    def logger(self):
        """获取当前类的日志器"""
        return logger.bind(name=self.__class__.__name__)
    
    def log_info(self, message: str, **kwargs):
        """记录信息日志"""
        self.logger.info(message, **kwargs)
    
    def log_debug(self, message: str, **kwargs):
        """记录调试日志"""
        self.logger.debug(message, **kwargs)
    
    def log_warning(self, message: str, **kwargs):
        """记录警告日志"""
        self.logger.warning(message, **kwargs)
    
    def log_error(self, message: str, **kwargs):
        """记录错误日志"""
        self.logger.error(message, **kwargs)
    
    def log_exception(self, message: str, **kwargs):
        """记录异常日志"""
        self.logger.exception(message, **kwargs)


def configure_development_logging():
    """配置开发环境日志"""
    setup_logger(
        log_level="DEBUG",
        log_file="logs/development.log",
        log_rotation="5 MB",
        log_retention="7 days"
    )


def configure_production_logging():
    """配置生产环境日志"""
    setup_logger(
        log_level="INFO",
        log_file="logs/production.log",
        log_rotation="50 MB",
        log_retention="90 days"
    )


def configure_testing_logging():
    """配置测试环境日志"""
    setup_logger(
        log_level="WARNING",
        log_file=None  # 测试时只输出到控制台
    )