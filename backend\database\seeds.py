import uuid
import json
from datetime import datetime
from typing import Dict, Any, List
from loguru import logger

from ..utils.database import get_db_manager


def create_sample_project() -> Dict[str, Any]:
    """创建示例项目"""
    return {
        "id": str(uuid.uuid4()),
        "name": "示例项目",
        "description": "这是一个示例项目，展示了串口通信测试的基本功能",
        "config": {
            "serial_config": {
                "port": "COM1",
                "baud_rate": 9600,
                "data_bits": 8,
                "stop_bits": 1,
                "parity": "N",
                "timeout": 1.0
            },
            "display_format": "hex",
            "auto_scroll": True,
            "max_history": 1000
        }
    }


def create_sample_protocols() -> List[Dict[str, Any]]:
    """创建示例协议"""
    protocols = []
    
    # 简单数据协议
    protocols.append({
        "id": str(uuid.uuid4()),
        "name": "简单数据协议",
        "description": "用于传输简单数值数据的协议",
        "fields": [
            {
                "name": "header",
                "type": "uint8",
                "size": 1,
                "description": "数据包头",
                "default_value": 0xAA
            },
            {
                "name": "length",
                "type": "uint8",
                "size": 1,
                "description": "数据长度"
            },
            {
                "name": "data",
                "type": "bytes",
                "size": "variable",
                "description": "数据内容"
            },
            {
                "name": "checksum",
                "type": "uint8",
                "size": 1,
                "description": "校验和"
            }
        ]
    })
    
    # 传感器数据协议
    protocols.append({
        "id": str(uuid.uuid4()),
        "name": "传感器数据协议",
        "description": "用于传输传感器数据的协议",
        "fields": [
            {
                "name": "sync",
                "type": "uint16",
                "size": 2,
                "description": "同步字",
                "default_value": 0x55AA
            },
            {
                "name": "sensor_id",
                "type": "uint8",
                "size": 1,
                "description": "传感器ID"
            },
            {
                "name": "temperature",
                "type": "float32",
                "size": 4,
                "description": "温度值",
                "unit": "°C"
            },
            {
                "name": "humidity",
                "type": "float32",
                "size": 4,
                "description": "湿度值",
                "unit": "%"
            },
            {
                "name": "timestamp",
                "type": "uint32",
                "size": 4,
                "description": "时间戳"
            }
        ]
    })
    
    return protocols


def create_sample_interfaces() -> List[Dict[str, Any]]:
    """创建示例界面"""
    interfaces = []
    
    # 数据监控界面
    interfaces.append({
        "id": str(uuid.uuid4()),
        "name": "数据监控界面",
        "description": "实时监控串口数据的界面",
        "components": [
            {
                "id": "serial_status",
                "type": "text",
                "title": "串口状态",
                "position": {"x": 0, "y": 0, "width": 6, "height": 2},
                "config": {
                    "data_source": "serial.status",
                    "format": "text",
                    "color": "auto"
                }
            },
            {
                "id": "data_chart",
                "type": "chart",
                "title": "数据图表",
                "position": {"x": 6, "y": 0, "width": 6, "height": 6},
                "config": {
                    "chart_type": "line",
                    "data_source": "serial.data",
                    "x_field": "timestamp",
                    "y_field": "value",
                    "max_points": 100
                }
            },
            {
                "id": "send_data",
                "type": "input",
                "title": "发送数据",
                "position": {"x": 0, "y": 2, "width": 4, "height": 2},
                "config": {
                    "input_type": "text",
                    "placeholder": "输入要发送的数据",
                    "format": "hex"
                }
            },
            {
                "id": "send_button",
                "type": "button",
                "title": "发送",
                "position": {"x": 4, "y": 2, "width": 2, "height": 2},
                "config": {
                    "action": "send_serial_data",
                    "style": "primary"
                }
            }
        ],
        "layout": {
            "grid_size": 12,
            "row_height": 60,
            "margin": [10, 10]
        }
    })
    
    # 传感器监控界面
    interfaces.append({
        "id": str(uuid.uuid4()),
        "name": "传感器监控界面",
        "description": "监控传感器数据的专用界面",
        "components": [
            {
                "id": "temperature_gauge",
                "type": "gauge",
                "title": "温度",
                "position": {"x": 0, "y": 0, "width": 6, "height": 4},
                "config": {
                    "data_source": "sensor.temperature",
                    "min_value": -40,
                    "max_value": 80,
                    "unit": "°C",
                    "color_ranges": [
                        {"min": -40, "max": 0, "color": "blue"},
                        {"min": 0, "max": 25, "color": "green"},
                        {"min": 25, "max": 50, "color": "orange"},
                        {"min": 50, "max": 80, "color": "red"}
                    ]
                }
            },
            {
                "id": "humidity_gauge",
                "type": "gauge",
                "title": "湿度",
                "position": {"x": 6, "y": 0, "width": 6, "height": 4},
                "config": {
                    "data_source": "sensor.humidity",
                    "min_value": 0,
                    "max_value": 100,
                    "unit": "%",
                    "color_ranges": [
                        {"min": 0, "max": 30, "color": "red"},
                        {"min": 30, "max": 70, "color": "green"},
                        {"min": 70, "max": 100, "color": "blue"}
                    ]
                }
            },
            {
                "id": "data_table",
                "type": "table",
                "title": "历史数据",
                "position": {"x": 0, "y": 4, "width": 12, "height": 4},
                "config": {
                    "data_source": "sensor.history",
                    "columns": [
                        {"field": "timestamp", "title": "时间", "width": 150},
                        {"field": "temperature", "title": "温度(°C)", "width": 100},
                        {"field": "humidity", "title": "湿度(%)", "width": 100},
                        {"field": "sensor_id", "title": "传感器ID", "width": 100}
                    ],
                    "max_rows": 50
                }
            }
        ],
        "layout": {
            "grid_size": 12,
            "row_height": 60,
            "margin": [10, 10]
        }
    })
    
    return interfaces


def create_sample_flows() -> List[Dict[str, Any]]:
    """创建示例流程"""
    flows = []
    
    # 数据采集流程
    flows.append({
        "id": str(uuid.uuid4()),
        "name": "数据采集流程",
        "description": "定时采集串口数据并保存",
        "nodes": [
            {
                "id": "start",
                "type": "start",
                "position": {"x": 100, "y": 100},
                "config": {"title": "开始"}
            },
            {
                "id": "send_request",
                "type": "serial_send",
                "position": {"x": 300, "y": 100},
                "config": {
                    "title": "发送请求",
                    "data": "AA 01 BB",
                    "format": "hex"
                }
            },
            {
                "id": "wait_response",
                "type": "serial_receive",
                "position": {"x": 500, "y": 100},
                "config": {
                    "title": "等待响应",
                    "timeout": 5000,
                    "expected_length": 10
                }
            },
            {
                "id": "process_data",
                "type": "data_process",
                "position": {"x": 700, "y": 100},
                "config": {
                    "title": "处理数据",
                    "script": "// 解析接收到的数据\nconst data = input.data;\nreturn { temperature: data[2], humidity: data[3] };"
                }
            },
            {
                "id": "delay",
                "type": "delay",
                "position": {"x": 500, "y": 300},
                "config": {
                    "title": "延时",
                    "duration": 1000
                }
            },
            {
                "id": "end",
                "type": "end",
                "position": {"x": 900, "y": 100},
                "config": {"title": "结束"}
            }
        ],
        "edges": [
            {"id": "e1", "source": "start", "target": "send_request"},
            {"id": "e2", "source": "send_request", "target": "wait_response"},
            {"id": "e3", "source": "wait_response", "target": "process_data"},
            {"id": "e4", "source": "process_data", "target": "end"},
            {"id": "e5", "source": "process_data", "target": "delay"},
            {"id": "e6", "source": "delay", "target": "send_request"}
        ],
        "config": {
            "auto_start": False,
            "loop_mode": True,
            "max_iterations": 100
        }
    })
    
    # 设备测试流程
    flows.append({
        "id": str(uuid.uuid4()),
        "name": "设备测试流程",
        "description": "自动化设备功能测试",
        "nodes": [
            {
                "id": "start",
                "type": "start",
                "position": {"x": 100, "y": 100},
                "config": {"title": "开始测试"}
            },
            {
                "id": "test_connection",
                "type": "serial_send",
                "position": {"x": 300, "y": 100},
                "config": {
                    "title": "测试连接",
                    "data": "AT",
                    "format": "ascii"
                }
            },
            {
                "id": "check_response",
                "type": "condition",
                "position": {"x": 500, "y": 100},
                "config": {
                    "title": "检查响应",
                    "condition": "response.includes('OK')"
                }
            },
            {
                "id": "test_passed",
                "type": "log",
                "position": {"x": 700, "y": 50},
                "config": {
                    "title": "测试通过",
                    "message": "设备连接测试通过",
                    "level": "info"
                }
            },
            {
                "id": "test_failed",
                "type": "log",
                "position": {"x": 700, "y": 150},
                "config": {
                    "title": "测试失败",
                    "message": "设备连接测试失败",
                    "level": "error"
                }
            },
            {
                "id": "end",
                "type": "end",
                "position": {"x": 900, "y": 100},
                "config": {"title": "结束"}
            }
        ],
        "edges": [
            {"id": "e1", "source": "start", "target": "test_connection"},
            {"id": "e2", "source": "test_connection", "target": "check_response"},
            {"id": "e3", "source": "check_response", "target": "test_passed", "condition": "true"},
            {"id": "e4", "source": "check_response", "target": "test_failed", "condition": "false"},
            {"id": "e5", "source": "test_passed", "target": "end"},
            {"id": "e6", "source": "test_failed", "target": "end"}
        ],
        "config": {
            "auto_start": False,
            "loop_mode": False
        }
    })
    
    return flows


def create_sample_templates() -> List[Dict[str, Any]]:
    """创建示例模板"""
    templates = []
    
    # 基础串口测试模板
    templates.append({
        "id": str(uuid.uuid4()),
        "name": "基础串口测试",
        "description": "用于基础串口通信测试的项目模板",
        "category": "通信测试",
        "tags": ["串口", "基础", "测试"],
        "template_data": {
            "project": {
                "name": "基础串口测试项目",
                "description": "基础的串口通信测试项目",
                "config": {
                    "serial_config": {
                        "baud_rate": 9600,
                        "data_bits": 8,
                        "stop_bits": 1,
                        "parity": "N"
                    }
                }
            },
            "protocols": [
                {
                    "name": "基础协议",
                    "description": "简单的数据传输协议",
                    "fields": [
                        {"name": "header", "type": "uint8", "size": 1},
                        {"name": "data", "type": "bytes", "size": "variable"},
                        {"name": "checksum", "type": "uint8", "size": 1}
                    ]
                }
            ],
            "interfaces": [
                {
                    "name": "基础监控界面",
                    "description": "基础的数据监控界面",
                    "components": [
                        {"type": "text", "title": "状态显示"},
                        {"type": "input", "title": "数据输入"},
                        {"type": "button", "title": "发送"}
                    ]
                }
            ]
        }
    })
    
    # 传感器数据采集模板
    templates.append({
        "id": str(uuid.uuid4()),
        "name": "传感器数据采集",
        "description": "用于传感器数据采集和监控的项目模板",
        "category": "数据采集",
        "tags": ["传感器", "数据采集", "监控"],
        "template_data": {
            "project": {
                "name": "传感器数据采集项目",
                "description": "传感器数据的实时采集和监控",
                "config": {
                    "serial_config": {
                        "baud_rate": 115200,
                        "data_bits": 8,
                        "stop_bits": 1,
                        "parity": "N"
                    },
                    "sampling_interval": 1000
                }
            },
            "protocols": [
                {
                    "name": "传感器协议",
                    "description": "传感器数据传输协议",
                    "fields": [
                        {"name": "sync", "type": "uint16", "size": 2},
                        {"name": "sensor_id", "type": "uint8", "size": 1},
                        {"name": "temperature", "type": "float32", "size": 4},
                        {"name": "humidity", "type": "float32", "size": 4}
                    ]
                }
            ],
            "interfaces": [
                {
                    "name": "传感器监控界面",
                    "description": "传感器数据的实时监控界面",
                    "components": [
                        {"type": "gauge", "title": "温度计"},
                        {"type": "gauge", "title": "湿度计"},
                        {"type": "chart", "title": "历史趋势"},
                        {"type": "table", "title": "数据记录"}
                    ]
                }
            ]
        }
    })
    
    return templates


def seed_database() -> Dict[str, Any]:
    """填充示例数据
    
    Returns:
        操作结果
    """
    db_manager = get_db_manager()
    
    try:
        # 创建示例项目
        project = create_sample_project()
        project_id = project["id"]
        
        if not db_manager.insert_record("projects", project):
            raise Exception("插入示例项目失败")
        
        logger.info(f"创建示例项目: {project['name']}")
        
        # 创建示例协议
        protocols = create_sample_protocols()
        for protocol in protocols:
            protocol["project_id"] = project_id
            if not db_manager.insert_record("protocols", protocol):
                raise Exception(f"插入协议失败: {protocol['name']}")
            logger.info(f"创建示例协议: {protocol['name']}")
        
        # 创建示例界面
        interfaces = create_sample_interfaces()
        for interface in interfaces:
            interface["project_id"] = project_id
            if not db_manager.insert_record("interfaces", interface):
                raise Exception(f"插入界面失败: {interface['name']}")
            logger.info(f"创建示例界面: {interface['name']}")
        
        # 创建示例流程
        flows = create_sample_flows()
        for flow in flows:
            flow["project_id"] = project_id
            if not db_manager.insert_record("flows", flow):
                raise Exception(f"插入流程失败: {flow['name']}")
            logger.info(f"创建示例流程: {flow['name']}")
        
        # 创建示例模板
        templates = create_sample_templates()
        for template in templates:
            if not db_manager.insert_record("project_templates", template):
                raise Exception(f"插入模板失败: {template['name']}")
            logger.info(f"创建示例模板: {template['name']}")
        
        # 插入系统配置
        system_configs = [
            {
                "key": "app_version",
                "value": "1.0.0",
                "description": "应用程序版本"
            },
            {
                "key": "max_serial_history",
                "value": "10000",
                "description": "串口数据历史记录最大条数"
            },
            {
                "key": "auto_backup_enabled",
                "value": "true",
                "description": "是否启用自动备份"
            },
            {
                "key": "backup_interval_hours",
                "value": "24",
                "description": "自动备份间隔（小时）"
            }
        ]
        
        for config in system_configs:
            db_manager.execute_query(
                "INSERT OR REPLACE INTO system_config (key, value, description) VALUES (?, ?, ?)",
                (config["key"], config["value"], config["description"])
            )
        
        logger.info("示例数据创建完成")
        
        return {
            "success": True,
            "message": "示例数据创建成功",
            "project_id": project_id,
            "created": {
                "projects": 1,
                "protocols": len(protocols),
                "interfaces": len(interfaces),
                "flows": len(flows),
                "templates": len(templates),
                "configs": len(system_configs)
            }
        }
    
    except Exception as e:
        logger.error(f"创建示例数据失败: {e}")
        return {
            "success": False,
            "message": f"创建示例数据失败: {str(e)}"
        }


def clear_sample_data() -> Dict[str, Any]:
    """清除示例数据
    
    Returns:
        操作结果
    """
    db_manager = get_db_manager()
    
    try:
        # 删除所有示例数据
        tables = [
            "flow_executions",
            "serial_data_history",
            "flows",
            "interfaces",
            "protocols",
            "projects",
            "project_templates"
        ]
        
        deleted_counts = {}
        for table in tables:
            result = db_manager.execute_query(f"SELECT COUNT(*) as count FROM {table}", fetch_one=True)
            count_before = result['count'] if result else 0
            
            db_manager.execute_query(f"DELETE FROM {table}")
            
            result = db_manager.execute_query(f"SELECT COUNT(*) as count FROM {table}", fetch_one=True)
            count_after = result['count'] if result else 0
            
            deleted_counts[table] = count_before - count_after
        
        logger.info("示例数据清除完成")
        
        return {
            "success": True,
            "message": "示例数据清除成功",
            "deleted": deleted_counts
        }
    
    except Exception as e:
        logger.error(f"清除示例数据失败: {e}")
        return {
            "success": False,
            "message": f"清除示例数据失败: {str(e)}"
        }