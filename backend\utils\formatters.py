import re
from datetime import datetime, timedelta
from typing import Union, Optional, List


def format_timestamp(
    timestamp: Union[int, float, datetime],
    format_str: str = "%Y-%m-%d %H:%M:%S"
) -> str:
    """格式化时间戳
    
    Args:
        timestamp: 时间戳（秒）或datetime对象
        format_str: 格式化字符串
        
    Returns:
        格式化后的时间字符串
    """
    if isinstance(timestamp, datetime):
        dt = timestamp
    else:
        dt = datetime.fromtimestamp(timestamp)
    
    return dt.strftime(format_str)


def format_file_size(size_bytes: int) -> str:
    """格式化文件大小
    
    Args:
        size_bytes: 文件大小（字节）
        
    Returns:
        格式化后的文件大小字符串
    """
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    size = float(size_bytes)
    
    while size >= 1024.0 and i < len(size_names) - 1:
        size /= 1024.0
        i += 1
    
    return f"{size:.1f} {size_names[i]}"


def format_duration(seconds: Union[int, float]) -> str:
    """格式化持续时间
    
    Args:
        seconds: 持续时间（秒）
        
    Returns:
        格式化后的持续时间字符串
    """
    if seconds < 0:
        return "0秒"
    
    delta = timedelta(seconds=seconds)
    days = delta.days
    hours, remainder = divmod(delta.seconds, 3600)
    minutes, secs = divmod(remainder, 60)
    
    parts = []
    if days > 0:
        parts.append(f"{days}天")
    if hours > 0:
        parts.append(f"{hours}小时")
    if minutes > 0:
        parts.append(f"{minutes}分钟")
    if secs > 0 or not parts:
        parts.append(f"{secs}秒")
    
    return "".join(parts)


def format_hex_data(
    data: Union[bytes, List[int], str],
    separator: str = " ",
    uppercase: bool = True,
    prefix: str = "",
    bytes_per_line: Optional[int] = None
) -> str:
    """格式化十六进制数据
    
    Args:
        data: 要格式化的数据
        separator: 字节间分隔符
        uppercase: 是否使用大写字母
        prefix: 每个字节的前缀（如"0x"）
        bytes_per_line: 每行显示的字节数
        
    Returns:
        格式化后的十六进制字符串
    """
    if isinstance(data, str):
        # 如果是字符串，转换为字节
        data = data.encode('utf-8')
    elif isinstance(data, list):
        # 如果是整数列表，转换为字节
        data = bytes(data)
    
    hex_chars = []
    for i, byte in enumerate(data):
        hex_str = f"{prefix}{byte:02x}"
        if uppercase:
            hex_str = hex_str.upper()
        hex_chars.append(hex_str)
        
        # 如果指定了每行字节数，添加换行符
        if bytes_per_line and (i + 1) % bytes_per_line == 0 and i < len(data) - 1:
            hex_chars.append("\n")
    
    return separator.join(hex_chars)


def format_ascii_data(
    data: Union[bytes, List[int]],
    replace_non_printable: bool = True,
    replacement_char: str = "."
) -> str:
    """格式化ASCII数据
    
    Args:
        data: 要格式化的数据
        replace_non_printable: 是否替换不可打印字符
        replacement_char: 替换字符
        
    Returns:
        格式化后的ASCII字符串
    """
    if isinstance(data, list):
        data = bytes(data)
    
    result = []
    for byte in data:
        if replace_non_printable and (byte < 32 or byte > 126):
            result.append(replacement_char)
        else:
            result.append(chr(byte))
    
    return "".join(result)


def format_serial_data(
    data: bytes,
    display_format: str = "hex",
    show_timestamp: bool = True,
    timestamp: Optional[datetime] = None
) -> str:
    """格式化串口数据
    
    Args:
        data: 串口数据
        display_format: 显示格式（hex, ascii, both）
        show_timestamp: 是否显示时间戳
        timestamp: 时间戳（如果为None则使用当前时间）
        
    Returns:
        格式化后的串口数据字符串
    """
    if timestamp is None:
        timestamp = datetime.now()
    
    parts = []
    
    if show_timestamp:
        parts.append(f"[{format_timestamp(timestamp)}]")
    
    if display_format.lower() == "hex":
        parts.append(format_hex_data(data))
    elif display_format.lower() == "ascii":
        parts.append(format_ascii_data(data))
    elif display_format.lower() == "both":
        hex_str = format_hex_data(data)
        ascii_str = format_ascii_data(data)
        parts.append(f"HEX: {hex_str} | ASCII: {ascii_str}")
    
    return " ".join(parts)


def format_protocol_field(
    field_name: str,
    field_value: any,
    field_type: str,
    field_unit: Optional[str] = None
) -> str:
    """格式化协议字段
    
    Args:
        field_name: 字段名称
        field_value: 字段值
        field_type: 字段类型
        field_unit: 字段单位
        
    Returns:
        格式化后的字段字符串
    """
    value_str = str(field_value)
    
    # 根据字段类型格式化值
    if field_type.startswith("uint") or field_type.startswith("int"):
        if isinstance(field_value, (int, float)):
            value_str = f"{field_value:,}"
    elif field_type.startswith("float"):
        if isinstance(field_value, (int, float)):
            value_str = f"{field_value:.2f}"
    elif field_type == "bool":
        value_str = "是" if field_value else "否"
    
    # 添加单位
    if field_unit:
        value_str += f" {field_unit}"
    
    return f"{field_name}: {value_str}"


def format_error_message(
    error_type: str,
    error_message: str,
    error_code: Optional[str] = None,
    timestamp: Optional[datetime] = None
) -> str:
    """格式化错误消息
    
    Args:
        error_type: 错误类型
        error_message: 错误消息
        error_code: 错误代码
        timestamp: 时间戳
        
    Returns:
        格式化后的错误消息
    """
    if timestamp is None:
        timestamp = datetime.now()
    
    parts = [
        f"[{format_timestamp(timestamp)}]",
        f"[{error_type}]"
    ]
    
    if error_code:
        parts.append(f"[{error_code}]")
    
    parts.append(error_message)
    
    return " ".join(parts)


def format_progress(
    current: int,
    total: int,
    width: int = 50,
    fill_char: str = "█",
    empty_char: str = "░"
) -> str:
    """格式化进度条
    
    Args:
        current: 当前进度
        total: 总进度
        width: 进度条宽度
        fill_char: 填充字符
        empty_char: 空白字符
        
    Returns:
        格式化后的进度条字符串
    """
    if total <= 0:
        return f"[{empty_char * width}] 0.0%"
    
    percentage = min(current / total, 1.0)
    filled_width = int(width * percentage)
    empty_width = width - filled_width
    
    progress_bar = fill_char * filled_width + empty_char * empty_width
    percentage_str = f"{percentage * 100:.1f}%"
    
    return f"[{progress_bar}] {percentage_str}"


def format_table(
    headers: List[str],
    rows: List[List[str]],
    max_width: Optional[int] = None
) -> str:
    """格式化表格
    
    Args:
        headers: 表头
        rows: 数据行
        max_width: 最大列宽
        
    Returns:
        格式化后的表格字符串
    """
    if not headers or not rows:
        return ""
    
    # 计算每列的最大宽度
    col_widths = []
    for i, header in enumerate(headers):
        max_col_width = len(header)
        for row in rows:
            if i < len(row):
                max_col_width = max(max_col_width, len(str(row[i])))
        
        if max_width:
            max_col_width = min(max_col_width, max_width)
        
        col_widths.append(max_col_width)
    
    # 格式化表头
    header_row = " | ".join(header.ljust(col_widths[i]) for i, header in enumerate(headers))
    separator = "-+-".join("-" * width for width in col_widths)
    
    # 格式化数据行
    data_rows = []
    for row in rows:
        formatted_row = []
        for i, cell in enumerate(row):
            if i < len(col_widths):
                cell_str = str(cell)
                if max_width and len(cell_str) > max_width:
                    cell_str = cell_str[:max_width-3] + "..."
                formatted_row.append(cell_str.ljust(col_widths[i]))
        data_rows.append(" | ".join(formatted_row))
    
    # 组合表格
    table_lines = [header_row, separator] + data_rows
    return "\n".join(table_lines)


def format_json_pretty(data: any, indent: int = 2) -> str:
    """格式化JSON数据
    
    Args:
        data: 要格式化的数据
        indent: 缩进空格数
        
    Returns:
        格式化后的JSON字符串
    """
    import json
    return json.dumps(data, indent=indent, ensure_ascii=False, default=str)


def format_bytes_with_units(value: Union[int, float], unit: str = "B") -> str:
    """格式化带单位的字节数
    
    Args:
        value: 数值
        unit: 单位
        
    Returns:
        格式化后的字符串
    """
    if value >= 1024 ** 4:
        return f"{value / (1024 ** 4):.2f} T{unit}"
    elif value >= 1024 ** 3:
        return f"{value / (1024 ** 3):.2f} G{unit}"
    elif value >= 1024 ** 2:
        return f"{value / (1024 ** 2):.2f} M{unit}"
    elif value >= 1024:
        return f"{value / 1024:.2f} K{unit}"
    else:
        return f"{value} {unit}"