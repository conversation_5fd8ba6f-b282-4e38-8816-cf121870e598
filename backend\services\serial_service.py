import asyncio
import time
import uuid
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime
import serial
import serial.tools.list_ports
from loguru import logger
from threading import Thread, Lock

from backend.models.base import SerialPortConfig, SerialPortStatus, SerialData
from backend.websocket_manager import WebSocketManager
from backend.utils.database import get_db_manager


class SerialService:
    """串口通信服务"""
    
    def __init__(self, websocket_manager: Optional[WebSocketManager] = None):
        self.websocket_manager = websocket_manager
        self.db_manager = get_db_manager()
        self.serial_port: Optional[serial.Serial] = None
        self.config: Optional[SerialPortConfig] = None
        self.is_connected = False
        self.is_reading = False
        self.read_thread: Optional[Thread] = None
        self.data_history: List[SerialData] = []
        self.data_lock = Lock()
        self.max_history_size = 10000
        self.current_project_id: Optional[str] = None
        self.statistics = {
            "bytes_sent": 0,
            "bytes_received": 0,
            "messages_sent": 0,
            "messages_received": 0,
            "connection_time": None,
            "last_activity": None
        }
    
    async def get_available_ports(self) -> List[Dict[str, str]]:
        """获取可用串口列表"""
        try:
            ports = serial.tools.list_ports.comports()
            return [
                {
                    "port": port.device,
                    "description": port.description or "",
                    "hwid": port.hwid or "",
                    "manufacturer": getattr(port, 'manufacturer', '') or ""
                }
                for port in ports
            ]
        except Exception as e:
            logger.error(f"获取串口列表失败: {e}")
            return []
    
    async def get_status(self) -> SerialPortStatus:
        """获取串口状态"""
        return SerialPortStatus(
            is_connected=self.is_connected,
            port=self.config.port if self.config else None,
            config=self.config,
            statistics=self.statistics
        )
    
    async def connect(self, config: SerialPortConfig) -> bool:
        """连接串口"""
        try:
            if self.is_connected:
                await self.disconnect()
            
            self.config = config
            
            # 创建串口连接
            self.serial_port = serial.Serial(
                port=config.port,
                baudrate=config.baudrate,
                bytesize=config.bytesize,
                parity=config.parity,
                stopbits=config.stopbits,
                timeout=config.timeout
            )
            
            self.is_connected = True
            self.statistics["connection_time"] = datetime.now().isoformat()
            
            # 启动读取线程
            self._start_reading()
            
            logger.info(f"串口连接成功: {config.port}")
            
            # 通知前端连接状态
            if self.websocket_manager:
                await self.websocket_manager.send_system_notification(
                    "info",
                    f"串口连接成功: {config.port}",
                    {
                        "type": "serial_connected",
                        "port": config.port,
                        "config": config.dict()
                    }
                )
            
            return True
            
        except Exception as e:
            logger.error(f"串口连接失败: {e}")
            self.is_connected = False
            self.serial_port = None
            raise Exception(f"串口连接失败: {str(e)}")
    
    async def disconnect(self) -> bool:
        """断开串口连接"""
        try:
            self.is_connected = False
            
            # 停止读取线程
            if self.read_thread and self.read_thread.is_alive():
                self.is_reading = False
                self.read_thread.join(timeout=2)
            
            # 关闭串口
            if self.serial_port and self.serial_port.is_open:
                self.serial_port.close()
            
            port = self.config.port if self.config else "Unknown"
            self.serial_port = None
            self.config = None
            
            logger.info(f"串口断开连接: {port}")
            
            # 通知前端连接状态
            if self.websocket_manager:
                await self.websocket_manager.send_serial_status({
                    "type": "disconnected",
                    "port": port
                })
            
            return True
            
        except Exception as e:
            logger.error(f"串口断开失败: {e}")
            return False
    
    async def send_data(self, data: str, format_type: str = "ascii") -> bool:
        """发送数据"""
        try:
            if not self.is_connected or not self.serial_port:
                raise Exception("串口未连接")
            
            # 数据格式转换
            if format_type == "hex":
                # 移除空格和非十六进制字符
                hex_data = ''.join(c for c in data if c in '0123456789ABCDEFabcdef')
                if len(hex_data) % 2 != 0:
                    raise Exception("十六进制数据长度必须为偶数")
                bytes_data = bytes.fromhex(hex_data)
            else:
                bytes_data = data.encode('utf-8')
            
            # 发送数据
            self.serial_port.write(bytes_data)
            
            # 更新统计信息
            self.statistics["bytes_sent"] += len(bytes_data)
            self.statistics["messages_sent"] += 1
            self.statistics["last_activity"] = datetime.now().isoformat()
            
            # 记录发送数据
            serial_data = SerialData(
                direction="sent",
                data=data,
                format=format_type,
                timestamp=datetime.now(),
                bytes_count=len(bytes_data)
            )

            # 保存到内存历史
            with self.data_lock:
                self.data_history.append(serial_data)
                if len(self.data_history) > self.max_history_size:
                    self.data_history.pop(0)

            # 保存到数据库
            await self._save_serial_data_to_db(serial_data)

            # 通知前端
            if self.websocket_manager:
                await self.websocket_manager.send_serial_data(serial_data.dict())
            
            logger.debug(f"串口发送数据: {data} ({format_type})")
            return True
            
        except Exception as e:
            logger.error(f"串口发送数据失败: {e}")
            raise Exception(f"发送数据失败: {str(e)}")
    
    def _start_reading(self):
        """启动数据读取线程"""
        self.is_reading = True
        self.read_thread = Thread(target=self._read_data, daemon=True)
        self.read_thread.start()
    
    def _read_data(self):
        """读取串口数据"""
        buffer = b""
        
        while self.is_reading and self.serial_port and self.serial_port.is_open:
            try:
                if self.serial_port.in_waiting > 0:
                    data = self.serial_port.read(self.serial_port.in_waiting)
                    buffer += data
                    
                    # 按行分割数据（可配置分隔符）
                    while b'\n' in buffer:
                        line, buffer = buffer.split(b'\n', 1)
                        if line:
                            self._process_received_data(line + b'\n')
                    
                    # 处理没有换行符的数据（超时处理）
                    if buffer and len(buffer) > 1024:  # 缓冲区过大时强制处理
                        self._process_received_data(buffer)
                        buffer = b""
                
                time.sleep(0.01)  # 避免CPU占用过高
                
            except Exception as e:
                if self.is_reading:  # 只在正常读取时记录错误
                    logger.error(f"串口读取数据失败: {e}")
                break
    
    def _process_received_data(self, data: bytes):
        """处理接收到的数据"""
        try:
            # 尝试解码为ASCII
            try:
                ascii_data = data.decode('utf-8').strip()
                format_type = "ascii"
            except UnicodeDecodeError:
                ascii_data = data.hex().upper()
                format_type = "hex"
            
            # 更新统计信息
            self.statistics["bytes_received"] += len(data)
            self.statistics["messages_received"] += 1
            self.statistics["last_activity"] = datetime.now().isoformat()
            
            # 记录接收数据
            serial_data = SerialData(
                direction="received",
                data=ascii_data,
                format=format_type,
                timestamp=datetime.now(),
                bytes_count=len(data)
            )
            
            # 保存到内存历史
            with self.data_lock:
                self.data_history.append(serial_data)
                if len(self.data_history) > self.max_history_size:
                    self.data_history.pop(0)

            # 异步保存到数据库
            asyncio.create_task(self._save_serial_data_to_db(serial_data))

            # 异步通知前端
            if self.websocket_manager:
                asyncio.create_task(
                    self.websocket_manager.send_serial_data(serial_data.dict())
                )
            
            logger.debug(f"串口接收数据: {ascii_data} ({format_type})")
            
        except Exception as e:
            logger.error(f"处理接收数据失败: {e}")
    
    async def get_data_history(
        self, 
        page: int = 1, 
        page_size: int = 100,
        direction: Optional[str] = None,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None
    ) -> Tuple[List[SerialData], int]:
        """获取数据历史"""
        with self.data_lock:
            filtered_data = self.data_history.copy()
        
        # 过滤条件
        if direction:
            filtered_data = [d for d in filtered_data if d.direction == direction]
        
        if start_time:
            filtered_data = [d for d in filtered_data if d.timestamp >= start_time]
        
        if end_time:
            filtered_data = [d for d in filtered_data if d.timestamp <= end_time]
        
        # 分页
        total = len(filtered_data)
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        
        return filtered_data[start_idx:end_idx], total
    
    async def clear_data_history(self) -> bool:
        """清空数据历史"""
        try:
            with self.data_lock:
                self.data_history.clear()
            
            logger.info("串口数据历史已清空")
            return True
            
        except Exception as e:
            logger.error(f"清空数据历史失败: {e}")
            return False
    
    async def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = self.statistics.copy()
        stats["data_history_count"] = len(self.data_history)
        stats["is_connected"] = self.is_connected
        stats["current_config"] = self.config.dict() if self.config else None
        return stats
    
    async def test_connection(self, config: SerialPortConfig) -> Dict[str, Any]:
        """测试串口连接"""
        test_result = {
            "success": False,
            "message": "",
            "port_available": False,
            "can_open": False,
            "response_time": None
        }
        
        start_time = time.time()
        
        try:
            # 检查端口是否存在
            available_ports = await self.get_available_ports()
            port_exists = any(p["port"] == config.port for p in available_ports)
            
            if not port_exists:
                test_result["message"] = f"端口 {config.port} 不存在"
                return test_result
            
            test_result["port_available"] = True
            
            # 尝试打开端口
            test_serial = serial.Serial(
                port=config.port,
                baudrate=config.baudrate,
                bytesize=config.bytesize,
                parity=config.parity,
                stopbits=config.stopbits,
                timeout=1
            )
            
            test_result["can_open"] = True
            
            # 简单的读写测试
            test_serial.write(b"\r\n")
            time.sleep(0.1)
            
            if test_serial.in_waiting > 0:
                test_serial.read(test_serial.in_waiting)
            
            test_serial.close()
            
            test_result["success"] = True
            test_result["message"] = "连接测试成功"
            
        except Exception as e:
            test_result["message"] = f"连接测试失败: {str(e)}"
        
        test_result["response_time"] = round((time.time() - start_time) * 1000, 2)
        return test_result
    
    async def get_current_config(self) -> Optional[SerialPortConfig]:
        """获取当前配置"""
        return self.config
    
    async def update_config(self, config: SerialPortConfig) -> bool:
        """更新配置（需要重新连接）"""
        try:
            if self.is_connected:
                await self.disconnect()
                await self.connect(config)
            else:
                self.config = config
            
            return True

        except Exception as e:
            logger.error(f"更新配置失败: {e}")
            return False

    async def _save_serial_data_to_db(self, serial_data: SerialData):
        """保存串口数据到数据库"""
        try:
            data_dict = {
                "direction": serial_data.direction,
                "data": serial_data.data,
                "format": serial_data.format,
                "port": self.config.port if self.config else None,
                "timestamp": serial_data.timestamp.isoformat(),
                "bytes_count": serial_data.bytes_count,
                "project_id": self.current_project_id
            }

            self.db_manager.insert_record("serial_data_history", data_dict)

        except Exception as e:
            logger.error(f"保存串口数据到数据库失败: {e}")

    async def set_current_project(self, project_id: str):
        """设置当前项目ID"""
        self.current_project_id = project_id

    async def get_data_from_db(
        self,
        page: int = 1,
        page_size: int = 100,
        direction: Optional[str] = None,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        project_id: Optional[str] = None
    ) -> Tuple[List[Dict], int]:
        """从数据库获取串口数据历史"""
        try:
            # 构建查询条件
            conditions = []
            params = []

            if direction:
                conditions.append("direction = ?")
                params.append(direction)

            if start_time:
                conditions.append("timestamp >= ?")
                params.append(start_time.isoformat())

            if end_time:
                conditions.append("timestamp <= ?")
                params.append(end_time.isoformat())

            if project_id:
                conditions.append("project_id = ?")
                params.append(project_id)

            # 构建WHERE子句
            where_clause = ""
            if conditions:
                where_clause = "WHERE " + " AND ".join(conditions)

            # 获取总数
            count_query = f"SELECT COUNT(*) as total FROM serial_data_history {where_clause}"
            count_result = self.db_manager.execute_query(count_query, tuple(params), fetch_one=True)
            total = count_result['total'] if count_result else 0

            # 获取分页数据
            offset = (page - 1) * page_size
            data_query = f"""
                SELECT * FROM serial_data_history
                {where_clause}
                ORDER BY timestamp DESC
                LIMIT ? OFFSET ?
            """
            params.extend([page_size, offset])

            data_results = self.db_manager.execute_query(data_query, tuple(params))

            return data_results, total

        except Exception as e:
            logger.error(f"从数据库获取串口数据失败: {e}")
            return [], 0

    async def clear_data_history(self, project_id: Optional[str] = None) -> bool:
        """清除数据历史"""
        try:
            # 清除内存历史
            with self.data_lock:
                self.data_history.clear()

            # 清除数据库历史
            if project_id:
                self.db_manager.execute_query(
                    "DELETE FROM serial_data_history WHERE project_id = ?",
                    (project_id,)
                )
            else:
                self.db_manager.execute_query("DELETE FROM serial_data_history")

            logger.info("串口数据历史已清除")
            return True

        except Exception as e:
            logger.error(f"清除数据历史失败: {e}")
            return False

    async def export_data_history(
        self,
        file_path: str,
        format_type: str = "csv",
        project_id: Optional[str] = None
    ) -> bool:
        """导出数据历史"""
        try:
            # 获取所有数据
            data_results, _ = await self.get_data_from_db(
                page=1,
                page_size=999999,  # 获取所有数据
                project_id=project_id
            )

            if format_type.lower() == "csv":
                import csv
                with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                    if data_results:
                        fieldnames = data_results[0].keys()
                        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                        writer.writeheader()
                        writer.writerows(data_results)

            elif format_type.lower() == "json":
                import json
                with open(file_path, 'w', encoding='utf-8') as jsonfile:
                    json.dump(data_results, jsonfile, ensure_ascii=False, indent=2, default=str)

            else:
                raise ValueError(f"不支持的导出格式: {format_type}")

            logger.info(f"数据历史已导出到: {file_path}")
            return True

        except Exception as e:
            logger.error(f"导出数据历史失败: {e}")
            return False