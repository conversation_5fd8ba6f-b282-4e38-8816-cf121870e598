import React, { useState, useCallback } from 'react'
import { Card, Row, Col, Button, Space, Drawer, Form, Input, Select, InputNumber, message } from 'antd'
import { PlayCircleOutlined, StopOutlined, SaveOutlined, FolderOpenOutlined } from '@ant-design/icons'

import ReactFlow, {
  Node,
  addEdge,
  Background,
  Controls,
  MiniMap,
  useNodesState,
  useEdgesState,
  Connection,
  NodeTypes,
  NodeProps,
} from 'reactflow'
import 'reactflow/dist/style.css'

// 自定义节点类型
const CustomNode: React.FC<NodeProps<FlowNodeData>> = ({ data, selected }) => {
  const getNodeStyle = () => {
    const baseStyle = {
      padding: '10px',
      borderRadius: '8px',
      border: selected ? '2px solid #1890ff' : '1px solid #d9d9d9',
      background: 'white',
      minWidth: '120px',
      textAlign: 'center' as const,
    }

    switch (data.type) {
      case 'start':
        return { ...baseStyle, background: '#52c41a', color: 'white' }
      case 'end':
        return { ...baseStyle, background: '#ff4d4f', color: 'white' }
      case 'condition':
        return { ...baseStyle, background: '#faad14', color: 'white', transform: 'rotate(45deg)' }
      case 'action':
        return { ...baseStyle, background: '#1890ff', color: 'white' }
      case 'delay':
        return { ...baseStyle, background: '#722ed1', color: 'white' }
      default:
        return baseStyle
    }
  }

  return (
    <div style={getNodeStyle()}>
      <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>{data.label}</div>
      {data.description && (
        <div style={{ fontSize: '12px', opacity: 0.8 }}>{data.description}</div>
      )}
    </div>
  )
}

const nodeTypes: NodeTypes = {
  custom: CustomNode,
}

interface FlowNodeData {
  type: 'start' | 'end' | 'condition' | 'action' | 'delay';
  label: string;
  description?: string;
  config?: Record<string, any>;
}

type FlowNode = Node<FlowNodeData>

const FlowEngine: React.FC = () => {
  const [nodes, setNodes, onNodesChange] = useNodesState<FlowNodeData>([])
  const [edges, setEdges, onEdgesChange] = useEdgesState([])
  const [selectedNode, setSelectedNode] = useState<Node<FlowNodeData> | null>(null)
  const [drawerVisible, setDrawerVisible] = useState(false)
  const [isRunning, setIsRunning] = useState(false)
  const [form] = Form.useForm()

  const nodeTemplates = [
    { type: 'start', label: '开始', icon: '🟢', description: '流程开始节点' },
    { type: 'end', label: '结束', icon: '🔴', description: '流程结束节点' },
    { type: 'condition', label: '条件判断', icon: '🔶', description: '根据条件分支' },
    { type: 'action', label: '执行动作', icon: '🔵', description: '执行具体操作' },
    { type: 'delay', label: '延时等待', icon: '🟣', description: '等待指定时间' },
  ]

  const onConnect = useCallback(
    (params: Connection) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  )

  const addNode = (type: FlowNodeData['type']) => {
    const template = nodeTemplates.find(t => t.type === type)
    if (!template) return

    const newNode: Node<FlowNodeData> = {
      id: Date.now().toString(),
      type: 'custom',
      position: { 
        x: Math.random() * 400 + 100, 
        y: Math.random() * 300 + 100 
      },
      data: {
        type,
        label: template.label,
        description: template.description,
        config: getDefaultConfig(type),
      },
    } as FlowNode

    setNodes((nds) => [...nds, newNode])
  }

  const getDefaultConfig = (type: FlowNode['data']['type']) => {
    switch (type) {
      case 'condition':
        return {
          variable: '',
          operator: '==',
          value: '',
          trueAction: '',
          falseAction: '',
        }
      case 'action':
        return {
          actionType: 'send_command',
          command: '',
          parameters: {},
        }
      case 'delay':
        return {
          duration: 1000,
          unit: 'ms',
        }
      default:
        return {}
    }
  }

  const onNodeClick = (_: React.MouseEvent, node: FlowNode) => {
    setSelectedNode(node)
    form.setFieldsValue({
      label: node.data.label,
      description: node.data.description,
      ...node.data.config,
    })
    setDrawerVisible(true)
  }

  const handleConfigSave = () => {
    if (!selectedNode) return

    form.validateFields().then(values => {
      const { label, description, ...config } = values
      
      setNodes((nds) =>
        nds.map((node) =>
          node.id === selectedNode.id
            ? {
                ...node,
                data: {
                  ...node.data,
                  label,
                  description,
                  config,
                },
              }
            : node
        )
      )
      
      setDrawerVisible(false)
    })
  }

  const deleteNode = () => {
    if (!selectedNode) return
    
    setNodes((nds) => nds.filter((node) => node.id !== selectedNode.id))
    setEdges((eds) => eds.filter((edge) => 
      edge.source !== selectedNode.id && edge.target !== selectedNode.id
    ))
    setDrawerVisible(false)
  }

  const runFlow = () => {
    if (nodes.length === 0) {
      message.warning('请先添加流程节点')
      return
    }

    const startNode = nodes.find(node => node.data.type === 'start')
    if (!startNode) {
      message.warning('请添加开始节点')
      return
    }

    setIsRunning(true)
    message.success('流程开始执行')
    
    // 模拟流程执行
    setTimeout(() => {
      setIsRunning(false)
      message.success('流程执行完成')
    }, 3000)
  }

  const stopFlow = () => {
    setIsRunning(false)
    message.info('流程已停止')
  }

  const saveFlow = () => {
    const flowData = {
      nodes,
      edges,
      timestamp: new Date().toISOString(),
    }
    
    // 这里可以保存到后端或本地存储
    console.log('保存流程:', flowData)
    message.success('流程已保存')
  }

  return (
    <div style={{ padding: '0 24px' }}>
      <Row gutter={16}>
        {/* 节点工具栏 */}
        <Col span={4}>
          <Card title="节点库" size="small" style={{ marginBottom: 16 }}>
            <Space direction="vertical" style={{ width: '100%' }}>
              {nodeTemplates.map(template => (
                <Button
                  key={template.type}
                  block
                  onClick={() => addNode(template.type as FlowNode['data']['type'])}
                  style={{ textAlign: 'left' }}
                >
                  {template.icon} {template.label}
                </Button>
              ))}
            </Space>
          </Card>

          <Card title="流程控制" size="small">
            <Space direction="vertical" style={{ width: '100%' }}>
              <Button 
                type="primary" 
                icon={<PlayCircleOutlined />}
                onClick={runFlow}
                disabled={isRunning}
                block
              >
                运行流程
              </Button>
              <Button 
                icon={<StopOutlined />}
                onClick={stopFlow}
                disabled={!isRunning}
                block
              >
                停止流程
              </Button>
              <Button 
                icon={<SaveOutlined />}
                onClick={saveFlow}
                block
              >
                保存流程
              </Button>
              <Button 
                icon={<FolderOpenOutlined />}
                block
              >
                加载流程
              </Button>
            </Space>
          </Card>
        </Col>
        
        {/* 流程设计画布 */}
        <Col span={20}>
          <Card 
            title="流程设计器" 
            extra={
              <Space>
                <span style={{ 
                  color: isRunning ? '#52c41a' : '#999',
                  fontWeight: 'bold'
                }}>
                  {isRunning ? '● 运行中' : '○ 已停止'}
                </span>
              </Space>
            }
          >
            <div style={{ height: 600, border: '1px solid #d9d9d9', borderRadius: 6 }}>
              <ReactFlow
                nodes={nodes}
                edges={edges}
                onNodesChange={onNodesChange}
                onEdgesChange={onEdgesChange}
                onConnect={onConnect}
                onNodeClick={onNodeClick}
                nodeTypes={nodeTypes}
                fitView
              >
                <Background />
                <Controls />
                <MiniMap 
                  style={{
                    height: 120,
                    width: 200,
                  }}
                  zoomable
                  pannable
                />
              </ReactFlow>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 节点配置抽屉 */}
      <Drawer
        title="节点配置"
        placement="right"
        width={400}
        open={drawerVisible}
        onClose={() => setDrawerVisible(false)}
        extra={
          <Space>
            <Button onClick={() => setDrawerVisible(false)}>取消</Button>
            <Button danger onClick={deleteNode}>删除</Button>
            <Button type="primary" onClick={handleConfigSave}>保存</Button>
          </Space>
        }
      >
        {selectedNode && (
          <Form form={form} layout="vertical">
            <Form.Item label="节点名称" name="label">
              <Input />
            </Form.Item>
            
            <Form.Item label="节点描述" name="description">
              <Input.TextArea rows={2} />
            </Form.Item>

            {/* 根据节点类型显示不同的配置项 */}
            {selectedNode.data.type === 'condition' && (
              <>
                <Form.Item label="判断变量" name="variable">
                  <Select placeholder="选择变量">
                    <Select.Option value="temperature">温度</Select.Option>
                    <Select.Option value="voltage">电压</Select.Option>
                    <Select.Option value="current">电流</Select.Option>
                  </Select>
                </Form.Item>
                <Form.Item label="比较操作符" name="operator">
                  <Select>
                    <Select.Option value="==">等于</Select.Option>
                    <Select.Option value="!=">不等于</Select.Option>
                    <Select.Option value=">">&gt;</Select.Option>
                    <Select.Option value="<">&lt;</Select.Option>
                    <Select.Option value=">=">&gt;=</Select.Option>
                    <Select.Option value="<=">&lt;=</Select.Option>
                  </Select>
                </Form.Item>
                <Form.Item label="比较值" name="value">
                  <Input />
                </Form.Item>
              </>
            )}

            {selectedNode.data.type === 'action' && (
              <>
                <Form.Item label="动作类型" name="actionType">
                  <Select>
                    <Select.Option value="send_command">发送指令</Select.Option>
                    <Select.Option value="set_variable">设置变量</Select.Option>
                    <Select.Option value="call_function">调用函数</Select.Option>
                    <Select.Option value="log_message">记录日志</Select.Option>
                  </Select>
                </Form.Item>
                <Form.Item label="指令内容" name="command">
                  <Input.TextArea rows={3} placeholder="输入要执行的指令" />
                </Form.Item>
              </>
            )}

            {selectedNode.data.type === 'delay' && (
              <>
                <Form.Item label="延时时长" name="duration">
                  <InputNumber min={1} style={{ width: '100%' }} />
                </Form.Item>
                <Form.Item label="时间单位" name="unit">
                  <Select>
                    <Select.Option value="ms">毫秒</Select.Option>
                    <Select.Option value="s">秒</Select.Option>
                    <Select.Option value="m">分钟</Select.Option>
                  </Select>
                </Form.Item>
              </>
            )}
          </Form>
        )}
      </Drawer>
    </div>
  )
}

export default FlowEngine