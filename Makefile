# 串口通信测试工具 Makefile
# 用于简化开发、测试和部署流程

.PHONY: help install dev prod test clean lint format check build docker

# 默认目标
help:
	@echo "串口通信测试工具 - 可用命令:"
	@echo ""
	@echo "开发相关:"
	@echo "  install     - 安装所有依赖"
	@echo "  dev         - 启动开发环境"
	@echo "  prod        - 启动生产环境"
	@echo "  test        - 运行测试"
	@echo ""
	@echo "代码质量:"
	@echo "  lint        - 代码检查"
	@echo "  format      - 代码格式化"
	@echo "  check       - 完整代码检查"
	@echo ""
	@echo "构建部署:"
	@echo "  build       - 构建前端"
	@echo "  clean       - 清理临时文件"
	@echo "  docker      - 构建Docker镜像"
	@echo ""
	@echo "其他:"
	@echo "  backup      - 备份数据库"
	@echo "  migrate     - 运行数据库迁移"
	@echo "  seed        - 填充示例数据"

# 安装依赖
install:
	@echo "安装后端依赖..."
	cd backend && pip install -r requirements.txt
	@echo "安装前端依赖..."
	cd frontend && npm install
	@echo "创建必要目录..."
	mkdir -p data logs uploads backups
	@echo "依赖安装完成!"

# 开发环境
dev:
	@echo "启动开发环境..."
	python start.py --env development --reload

# 生产环境
prod:
	@echo "启动生产环境..."
	python start.py --env production --workers 4

# 测试环境
test-env:
	@echo "启动测试环境..."
	python start.py --env testing

# 运行测试
test:
	@echo "运行后端测试..."
	cd backend && python -m pytest tests/ -v
	@echo "运行前端测试..."
	cd frontend && npm test

# 运行特定测试
test-unit:
	@echo "运行单元测试..."
	cd backend && python -m pytest tests/ -m "unit" -v

test-integration:
	@echo "运行集成测试..."
	cd backend && python -m pytest tests/ -m "integration" -v

test-serial:
	@echo "运行串口测试..."
	cd backend && python -m pytest tests/ -m "serial" -v

# 代码检查
lint:
	@echo "运行代码检查..."
	cd backend && flake8 .
	cd backend && mypy .
	cd frontend && npm run lint

# 代码格式化
format:
	@echo "格式化后端代码..."
	cd backend && black .
	cd backend && isort .
	@echo "格式化前端代码..."
	cd frontend && npm run format

# 完整代码检查
check: format lint test
	@echo "代码检查完成!"

# 构建前端
build:
	@echo "构建前端应用..."
	cd frontend && npm run build
	@echo "前端构建完成!"

# 清理临时文件
clean:
	@echo "清理临时文件..."
	rm -rf backend/__pycache__
	rm -rf backend/**/__pycache__
	rm -rf backend/.pytest_cache
	rm -rf backend/.mypy_cache
	rm -rf backend/htmlcov
	rm -rf frontend/node_modules/.cache
	rm -rf frontend/dist
	rm -rf logs/*.log
	rm -rf *.pyc
	@echo "清理完成!"

# 深度清理
clean-all: clean
	@echo "深度清理..."
	rm -rf frontend/node_modules
	rm -rf backend/.venv
	rm -rf data/*.db
	rm -rf uploads/*
	@echo "深度清理完成!"

# 数据库操作
migrate:
	@echo "运行数据库迁移..."
	python -c "from backend.database.migrations import run_all_migrations; run_all_migrations()"

seed:
	@echo "填充示例数据..."
	python -c "from backend.database.seeds import seed_all_data; seed_all_data()"

backup:
	@echo "备份数据库..."
	python -c "from backend.utils.database import get_db_manager; get_db_manager().backup_database()"

# Docker相关
docker:
	@echo "构建Docker镜像..."
	docker build -t serial-communication-tool .

docker-run:
	@echo "运行Docker容器..."
	docker run -p 8000:8000 -v $(PWD)/data:/app/data serial-communication-tool

docker-compose:
	@echo "使用Docker Compose启动..."
	docker-compose up -d

# 环境检查
check-env:
	@echo "检查环境..."
	python start.py --check

# 安装开发工具
install-dev:
	@echo "安装开发工具..."
	pip install black isort flake8 mypy pytest pytest-asyncio pytest-cov
	cd frontend && npm install --save-dev

# 生成需求文件
freeze:
	@echo "生成requirements.txt..."
	cd backend && pip freeze > requirements-freeze.txt

# 更新依赖
update:
	@echo "更新后端依赖..."
	cd backend && pip install --upgrade -r requirements.txt
	@echo "更新前端依赖..."
	cd frontend && npm update

# 安全检查
security:
	@echo "运行安全检查..."
	cd backend && pip install safety bandit
	cd backend && safety check
	cd backend && bandit -r . -f json -o security-report.json
	cd frontend && npm audit

# 性能测试
perf:
	@echo "运行性能测试..."
	cd backend && python -m pytest tests/ -m "slow" -v

# 覆盖率报告
coverage:
	@echo "生成覆盖率报告..."
	cd backend && python -m pytest --cov=. --cov-report=html --cov-report=term
	@echo "覆盖率报告已生成到 backend/htmlcov/"

# 文档生成
docs:
	@echo "生成API文档..."
	python -c "import webbrowser; webbrowser.open('http://localhost:8000/docs')"

# 监控日志
logs:
	@echo "监控应用日志..."
	tail -f logs/app.log

# 快速重启
restart:
	@echo "重启应用..."
	pkill -f "python.*main.py" || true
	sleep 2
	make dev

# 版本信息
version:
	@echo "版本信息:"
	@python -c "from backend.core.config import settings; print(f'应用版本: {settings.VERSION}')"
	@python --version
	@cd frontend && node --version
	@cd frontend && npm --version