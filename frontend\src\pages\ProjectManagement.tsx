import React, { useState, useEffect } from 'react'
import { 
  Card, 
  Button, 
  Space, 
  Table, 
  Modal, 
  Form, 
  Input, 
  Select, 
  Popconfirm, 
  message, 
  Tabs,
  Upload,
  Tooltip,
  Tag
} from 'antd'
import { 
  PlusOutlined, 
  EditOutlined, 
  DeleteOutlined, 
  ExportOutlined, 
  ImportOutlined,
  CopyOutlined
} from '@ant-design/icons'
import type { UploadProps } from 'antd'
import type { ColumnsType } from 'antd/es/table'

interface Project {
  id: string
  name: string
  description: string
  createdAt: string
  updatedAt: string
  type: 'protocol' | 'interface' | 'flow'
  status: 'draft' | 'published'
}

interface Template {
  id: string
  name: string
  description: string
  type: 'protocol' | 'interface' | 'flow'
  tags: string[]
  createdAt: string
}

const ProjectManagement: React.FC = () => {
  const [projects, setProjects] = useState<Project[]>([])
  const [templates, setTemplates] = useState<Template[]>([])
  const [modalVisible, setModalVisible] = useState(false)
  const [modalType, setModalType] = useState<'create' | 'edit'>('create')
  const [selectedProject, setSelectedProject] = useState<Project | null>(null)
  const [form] = Form.useForm()

  // 模拟加载项目数据
  useEffect(() => {
    // 这里应该是从API获取数据
    const mockProjects: Project[] = [
      {
        id: '1',
        name: '温度监控协议',
        description: '用于温度传感器的通信协议',
        createdAt: '2023-05-10 14:30:00',
        updatedAt: '2023-05-15 09:45:00',
        type: 'protocol',
        status: 'published',
      },
      {
        id: '2',
        name: '电机控制面板',
        description: '电机参数监控与控制界面',
        createdAt: '2023-06-05 10:20:00',
        updatedAt: '2023-06-10 16:15:00',
        type: 'interface',
        status: 'draft',
      },
      {
        id: '3',
        name: '自动测试流程',
        description: '产品自动化测试流程',
        createdAt: '2023-07-12 11:30:00',
        updatedAt: '2023-07-20 14:25:00',
        type: 'flow',
        status: 'published',
      },
    ]
    
    setProjects(mockProjects)

    // 模拟模板数据
    const mockTemplates: Template[] = [
      {
        id: 't1',
        name: 'Modbus RTU协议模板',
        description: 'Modbus RTU通用协议模板',
        type: 'protocol',
        tags: ['通用', '工业'],
        createdAt: '2023-04-10 10:00:00',
      },
      {
        id: 't2',
        name: '数据监控界面模板',
        description: '通用数据监控界面布局',
        type: 'interface',
        tags: ['监控', '仪表盘'],
        createdAt: '2023-04-15 14:20:00',
      },
      {
        id: 't3',
        name: '设备自检流程模板',
        description: '设备上电自检标准流程',
        type: 'flow',
        tags: ['自检', '测试'],
        createdAt: '2023-04-20 09:30:00',
      },
    ]
    
    setTemplates(mockTemplates)
  }, [])

  const showCreateModal = () => {
    setModalType('create')
    setSelectedProject(null)
    form.resetFields()
    setModalVisible(true)
  }

  const showEditModal = (project: Project) => {
    setModalType('edit')
    setSelectedProject(project)
    form.setFieldsValue({
      name: project.name,
      description: project.description,
      type: project.type,
    })
    setModalVisible(true)
  }

  const handleModalOk = () => {
    form.validateFields().then(values => {
      if (modalType === 'create') {
        // 创建新项目
        const newProject: Project = {
          id: Date.now().toString(),
          name: values.name,
          description: values.description,
          type: values.type,
          createdAt: new Date().toLocaleString(),
          updatedAt: new Date().toLocaleString(),
          status: 'draft',
        }
        
        setProjects([...projects, newProject])
        message.success('项目创建成功')
      } else if (modalType === 'edit' && selectedProject) {
        // 更新项目
        const updatedProjects = projects.map(p => 
          p.id === selectedProject.id 
            ? { 
                ...p, 
                name: values.name, 
                description: values.description, 
                type: values.type,
                updatedAt: new Date().toLocaleString(),
              } 
            : p
        )
        
        setProjects(updatedProjects)
        message.success('项目更新成功')
      }
      
      setModalVisible(false)
    })
  }

  const handleDeleteProject = (id: string) => {
    setProjects(projects.filter(p => p.id !== id))
    message.success('项目删除成功')
  }

  const handleDuplicateProject = (project: Project) => {
    const newProject: Project = {
      ...project,
      id: Date.now().toString(),
      name: `${project.name} (副本)`,
      createdAt: new Date().toLocaleString(),
      updatedAt: new Date().toLocaleString(),
      status: 'draft',
    }
    
    setProjects([...projects, newProject])
    message.success('项目复制成功')
  }

  const handleExportProject = (project: Project) => {
    // 模拟导出项目
    const projectData = JSON.stringify(project, null, 2)
    const blob = new Blob([projectData], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${project.name}_${new Date().toISOString().slice(0, 10)}.json`
    a.click()
    URL.revokeObjectURL(url)
    
    message.success('项目导出成功')
  }

  const handleUseTemplate = (template: Template) => {
    // 从模板创建新项目
    const newProject: Project = {
      id: Date.now().toString(),
      name: `基于${template.name}的项目`,
      description: template.description,
      type: template.type,
      createdAt: new Date().toLocaleString(),
      updatedAt: new Date().toLocaleString(),
      status: 'draft',
    }
    
    setProjects([...projects, newProject])
    message.success('已从模板创建新项目')
  }

  const uploadProps: UploadProps = {
    name: 'file',
    action: 'https://run.mocky.io/v3/435e224c-44fb-4773-9faf-380c5e6a2188', // 模拟上传地址
    headers: {
      authorization: 'authorization-text',
    },
    onChange(info) {
      if (info.file.status === 'done') {
        message.success(`${info.file.name} 导入成功`)
        // 这里应该处理导入的项目数据
      } else if (info.file.status === 'error') {
        message.error(`${info.file.name} 导入失败`)
      }
    },
    showUploadList: false,
  }

  const projectColumns: ColumnsType<Project> = [
    {
      title: '项目名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: Project) => (
        <Space>
          {text}
          <Tag color={record.status === 'published' ? 'green' : 'orange'}>
            {record.status === 'published' ? '已发布' : '草稿'}
          </Tag>
        </Space>
      ),
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (type: Project['type']) => {
        const typeMap = {
          protocol: { text: '协议配置', color: '#1890ff' },
          interface: { text: '界面设计', color: '#52c41a' },
          flow: { text: '流程引擎', color: '#722ed1' },
        }
        return (
          <Tag color={typeMap[type].color}>
            {typeMap[type].text}
          </Tag>
        )
      },
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: Project) => (
        <Space size="small">
          <Tooltip title="编辑">
            <Button 
              type="text" 
              icon={<EditOutlined />} 
              onClick={() => showEditModal(record)} 
            />
          </Tooltip>
          <Tooltip title="复制">
            <Button 
              type="text" 
              icon={<CopyOutlined />} 
              onClick={() => handleDuplicateProject(record)} 
            />
          </Tooltip>
          <Tooltip title="导出">
            <Button 
              type="text" 
              icon={<ExportOutlined />} 
              onClick={() => handleExportProject(record)} 
            />
          </Tooltip>
          <Tooltip title="删除">
            <Popconfirm
              title="确定要删除此项目吗？"
              onConfirm={() => handleDeleteProject(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Button type="text" danger icon={<DeleteOutlined />} />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ]

  const templateColumns: ColumnsType<Template> = [
    {
      title: '模板名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (type: Template['type']) => {
        const typeMap = {
          protocol: { text: '协议配置', color: '#1890ff' },
          interface: { text: '界面设计', color: '#52c41a' },
          flow: { text: '流程引擎', color: '#722ed1' },
        }
        return (
          <Tag color={typeMap[type].color}>
            {typeMap[type].text}
          </Tag>
        )
      },
    },
    {
      title: '标签',
      dataIndex: 'tags',
      key: 'tags',
      render: (tags: string[]) => (
        <>
          {tags.map(tag => (
            <Tag key={tag}>{tag}</Tag>
          ))}
        </>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: Template) => (
        <Button 
          type="primary" 
          size="small"
          onClick={() => handleUseTemplate(record)}
        >
          使用此模板
        </Button>
      ),
    },
  ]

  return (
    <div style={{ padding: '0 24px' }}>
      <Tabs
        defaultActiveKey="projects"
        items={[
          {
            key: 'projects',
            label: '我的项目',
            children: (
              <Card
                title="项目管理"
                extra={
                  <Space>
                    <Upload {...uploadProps}>
                      <Button icon={<ImportOutlined />}>导入项目</Button>
                    </Upload>
                    <Button 
                      type="primary" 
                      icon={<PlusOutlined />}
                      onClick={showCreateModal}
                    >
                      新建项目
                    </Button>
                  </Space>
                }
              >
                <Table 
                  columns={projectColumns} 
                  dataSource={projects} 
                  rowKey="id"
                  pagination={{ pageSize: 10 }}
                />
              </Card>
            ),
          },
          {
            key: 'templates',
            label: '模板库',
            children: (
              <Card title="模板库">
                <Table 
                  columns={templateColumns} 
                  dataSource={templates} 
                  rowKey="id"
                  pagination={{ pageSize: 10 }}
                />
              </Card>
            ),
          },
        ]}
      />

      {/* 创建/编辑项目的模态框 */}
      <Modal
        title={modalType === 'create' ? '创建新项目' : '编辑项目'}
        open={modalVisible}
        onOk={handleModalOk}
        onCancel={() => setModalVisible(false)}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="name"
            label="项目名称"
            rules={[{ required: true, message: '请输入项目名称' }]}
          >
            <Input placeholder="请输入项目名称" />
          </Form.Item>
          
          <Form.Item
            name="description"
            label="项目描述"
          >
            <Input.TextArea rows={3} placeholder="请输入项目描述" />
          </Form.Item>
          
          <Form.Item
            name="type"
            label="项目类型"
            rules={[{ required: true, message: '请选择项目类型' }]}
          >
            <Select placeholder="请选择项目类型">
              <Select.Option value="protocol">协议配置</Select.Option>
              <Select.Option value="interface">界面设计</Select.Option>
              <Select.Option value="flow">流程引擎</Select.Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default ProjectManagement