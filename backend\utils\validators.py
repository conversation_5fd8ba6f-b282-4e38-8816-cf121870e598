import re
import json
import ipaddress
from typing import Any, Optional


def validate_email(email: str) -> bool:
    """验证邮箱地址格式
    
    Args:
        email: 邮箱地址
        
    Returns:
        是否为有效邮箱格式
    """
    if not email or not isinstance(email, str):
        return False
    
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))


def validate_ip_address(ip: str) -> bool:
    """验证IP地址格式（支持IPv4和IPv6）
    
    Args:
        ip: IP地址字符串
        
    Returns:
        是否为有效IP地址
    """
    if not ip or not isinstance(ip, str):
        return False
    
    try:
        ipaddress.ip_address(ip)
        return True
    except ValueError:
        return False


def validate_port(port: Any) -> bool:
    """验证端口号
    
    Args:
        port: 端口号（可以是字符串或整数）
        
    Returns:
        是否为有效端口号（1-65535）
    """
    try:
        port_num = int(port)
        return 1 <= port_num <= 65535
    except (ValueError, TypeError):
        return False


def validate_serial_port(port_name: str) -> bool:
    """验证串口名称格式
    
    Args:
        port_name: 串口名称
        
    Returns:
        是否为有效串口名称格式
    """
    if not port_name or not isinstance(port_name, str):
        return False
    
    # Windows: COM1, COM2, etc.
    # Linux/Mac: /dev/ttyUSB0, /dev/ttyACM0, etc.
    windows_pattern = r'^COM\d+$'
    linux_pattern = r'^/dev/tty(USB|ACM|S)\d+$'
    
    return bool(re.match(windows_pattern, port_name, re.IGNORECASE) or 
                re.match(linux_pattern, port_name))


def validate_hex_string(hex_str: str) -> bool:
    """验证十六进制字符串格式
    
    Args:
        hex_str: 十六进制字符串
        
    Returns:
        是否为有效十六进制字符串
    """
    if not hex_str or not isinstance(hex_str, str):
        return False
    
    # 移除可能的空格和分隔符
    cleaned = re.sub(r'[\s:-]', '', hex_str)
    
    # 检查是否只包含十六进制字符
    pattern = r'^[0-9A-Fa-f]+$'
    return bool(re.match(pattern, cleaned)) and len(cleaned) % 2 == 0


def validate_json_string(json_str: str) -> bool:
    """验证JSON字符串格式
    
    Args:
        json_str: JSON字符串
        
    Returns:
        是否为有效JSON格式
    """
    if not json_str or not isinstance(json_str, str):
        return False
    
    try:
        json.loads(json_str)
        return True
    except (json.JSONDecodeError, TypeError):
        return False


def validate_baud_rate(baud_rate: Any) -> bool:
    """验证波特率
    
    Args:
        baud_rate: 波特率值
        
    Returns:
        是否为有效波特率
    """
    try:
        rate = int(baud_rate)
        # 常见的波特率值
        valid_rates = [
            110, 300, 600, 1200, 2400, 4800, 9600, 14400, 
            19200, 38400, 57600, 115200, 128000, 256000
        ]
        return rate in valid_rates
    except (ValueError, TypeError):
        return False


def validate_data_bits(data_bits: Any) -> bool:
    """验证数据位
    
    Args:
        data_bits: 数据位数
        
    Returns:
        是否为有效数据位数（5-8）
    """
    try:
        bits = int(data_bits)
        return 5 <= bits <= 8
    except (ValueError, TypeError):
        return False


def validate_stop_bits(stop_bits: Any) -> bool:
    """验证停止位
    
    Args:
        stop_bits: 停止位数
        
    Returns:
        是否为有效停止位数（1, 1.5, 2）
    """
    try:
        bits = float(stop_bits)
        return bits in [1, 1.5, 2]
    except (ValueError, TypeError):
        return False


def validate_parity(parity: str) -> bool:
    """验证校验位
    
    Args:
        parity: 校验位类型
        
    Returns:
        是否为有效校验位类型
    """
    if not parity or not isinstance(parity, str):
        return False
    
    valid_parity = ['N', 'E', 'O', 'M', 'S']  # None, Even, Odd, Mark, Space
    return parity.upper() in valid_parity


def validate_timeout(timeout: Any) -> bool:
    """验证超时时间
    
    Args:
        timeout: 超时时间（秒）
        
    Returns:
        是否为有效超时时间
    """
    if timeout is None:
        return True
    
    try:
        time_val = float(timeout)
        return time_val >= 0
    except (ValueError, TypeError):
        return False


def validate_protocol_field_type(field_type: str) -> bool:
    """验证协议字段类型
    
    Args:
        field_type: 字段类型
        
    Returns:
        是否为有效字段类型
    """
    if not field_type or not isinstance(field_type, str):
        return False
    
    valid_types = [
        'uint8', 'int8', 'uint16', 'int16', 'uint32', 'int32',
        'uint64', 'int64', 'float32', 'float64', 'string', 'bytes',
        'bool', 'array', 'object'
    ]
    return field_type.lower() in valid_types


def validate_byte_order(byte_order: str) -> bool:
    """验证字节序
    
    Args:
        byte_order: 字节序
        
    Returns:
        是否为有效字节序
    """
    if not byte_order or not isinstance(byte_order, str):
        return False
    
    valid_orders = ['big', 'little']
    return byte_order.lower() in valid_orders


def validate_component_type(component_type: str) -> bool:
    """验证组件类型
    
    Args:
        component_type: 组件类型
        
    Returns:
        是否为有效组件类型
    """
    if not component_type or not isinstance(component_type, str):
        return False
    
    valid_types = [
        'text', 'number', 'chart', 'gauge', 'button', 'switch',
        'input', 'select', 'slider', 'progress', 'table', 'list',
        'image', 'video', 'iframe', 'container', 'tabs', 'modal'
    ]
    return component_type.lower() in valid_types


def validate_flow_node_type(node_type: str) -> bool:
    """验证流程节点类型
    
    Args:
        node_type: 节点类型
        
    Returns:
        是否为有效节点类型
    """
    if not node_type or not isinstance(node_type, str):
        return False
    
    valid_types = [
        'start', 'end', 'serial_send', 'serial_receive', 'delay',
        'condition', 'loop', 'variable', 'function', 'log',
        'notification', 'data_process', 'file_operation'
    ]
    return node_type.lower() in valid_types


def validate_file_extension(filename: str, allowed_extensions: list) -> bool:
    """验证文件扩展名
    
    Args:
        filename: 文件名
        allowed_extensions: 允许的扩展名列表
        
    Returns:
        是否为允许的文件扩展名
    """
    if not filename or not isinstance(filename, str):
        return False
    
    if not allowed_extensions:
        return True
    
    file_ext = filename.lower().split('.')[-1] if '.' in filename else ''
    return file_ext in [ext.lower().lstrip('.') for ext in allowed_extensions]


def validate_range(value: Any, min_val: Optional[float] = None, max_val: Optional[float] = None) -> bool:
    """验证数值范围
    
    Args:
        value: 要验证的值
        min_val: 最小值（可选）
        max_val: 最大值（可选）
        
    Returns:
        是否在指定范围内
    """
    try:
        num_val = float(value)
        
        if min_val is not None and num_val < min_val:
            return False
        
        if max_val is not None and num_val > max_val:
            return False
        
        return True
    except (ValueError, TypeError):
        return False


def validate_string_length(text: str, min_length: int = 0, max_length: Optional[int] = None) -> bool:
    """验证字符串长度
    
    Args:
        text: 要验证的字符串
        min_length: 最小长度
        max_length: 最大长度（可选）
        
    Returns:
        是否符合长度要求
    """
    if not isinstance(text, str):
        return False
    
    length = len(text)
    
    if length < min_length:
        return False
    
    if max_length is not None and length > max_length:
        return False
    
    return True