from typing import List, Optional
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from loguru import logger

from backend.models.base import ResponseModel, Protocol, ProtocolField, PaginationModel
from backend.services.protocol_service import ProtocolService

router = APIRouter()

# 获取协议服务实例
def get_protocol_service() -> ProtocolService:
    return ProtocolService()


class ProtocolCreateRequest(BaseModel):
    """协议创建请求模型"""
    name: str
    description: str = ""
    total_length: int
    header: str = ""
    footer: str = ""
    fields: List[ProtocolField] = []


class ProtocolUpdateRequest(BaseModel):
    """协议更新请求模型"""
    name: Optional[str] = None
    description: Optional[str] = None
    total_length: Optional[int] = None
    header: Optional[str] = None
    footer: Optional[str] = None
    fields: Optional[List[ProtocolField]] = None


class ProtocolTestRequest(BaseModel):
    """协议测试请求模型"""
    protocol_id: str
    test_data: str
    data_format: str = "hex"  # hex, ascii, binary


@router.get("/", response_model=ResponseModel)
async def get_protocols(
    page: int = 1,
    page_size: int = 10,
    search: Optional[str] = None,
    protocol_service: ProtocolService = Depends(get_protocol_service)
):
    """获取协议列表"""
    try:
        protocols, total = await protocol_service.get_protocols(
            page=page, page_size=page_size, search=search
        )
        pagination = PaginationModel(
            page=page,
            page_size=page_size,
            total=total,
            total_pages=(total + page_size - 1) // page_size
        )
        return ResponseModel(
            data={"protocols": protocols, "pagination": pagination},
            message="获取协议列表成功"
        )
    except Exception as e:
        logger.error(f"获取协议列表失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{protocol_id}", response_model=ResponseModel)
async def get_protocol(
    protocol_id: str,
    protocol_service: ProtocolService = Depends(get_protocol_service)
):
    """获取协议详情"""
    try:
        protocol = await protocol_service.get_protocol(protocol_id)
        if not protocol:
            raise HTTPException(status_code=404, detail="协议不存在")
        return ResponseModel(data=protocol, message="获取协议详情成功")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取协议详情失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/", response_model=ResponseModel)
async def create_protocol(
    request: ProtocolCreateRequest,
    protocol_service: ProtocolService = Depends(get_protocol_service)
):
    """创建协议"""
    try:
        protocol = await protocol_service.create_protocol(
            name=request.name,
            description=request.description,
            total_length=request.total_length,
            header=request.header,
            footer=request.footer,
            fields=request.fields
        )
        return ResponseModel(data=protocol, message="协议创建成功")
    except Exception as e:
        logger.error(f"协议创建失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/{protocol_id}", response_model=ResponseModel)
async def update_protocol(
    protocol_id: str,
    request: ProtocolUpdateRequest,
    protocol_service: ProtocolService = Depends(get_protocol_service)
):
    """更新协议"""
    try:
        protocol = await protocol_service.update_protocol(protocol_id, request.dict(exclude_unset=True))
        if not protocol:
            raise HTTPException(status_code=404, detail="协议不存在")
        return ResponseModel(data=protocol, message="协议更新成功")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"协议更新失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{protocol_id}", response_model=ResponseModel)
async def delete_protocol(
    protocol_id: str,
    protocol_service: ProtocolService = Depends(get_protocol_service)
):
    """删除协议"""
    try:
        success = await protocol_service.delete_protocol(protocol_id)
        if not success:
            raise HTTPException(status_code=404, detail="协议不存在")
        return ResponseModel(message="协议删除成功")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"协议删除失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/test", response_model=ResponseModel)
async def test_protocol(
    request: ProtocolTestRequest,
    protocol_service: ProtocolService = Depends(get_protocol_service)
):
    """测试协议解析"""
    try:
        result = await protocol_service.test_protocol(
            protocol_id=request.protocol_id,
            test_data=request.test_data,
            data_format=request.data_format
        )
        return ResponseModel(data=result, message="协议测试完成")
    except Exception as e:
        logger.error(f"协议测试失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{protocol_id}/parse", response_model=ResponseModel)
async def parse_data(
    protocol_id: str,
    data: str,
    data_format: str = "hex",
    protocol_service: ProtocolService = Depends(get_protocol_service)
):
    """使用协议解析数据"""
    try:
        result = await protocol_service.parse_data(
            protocol_id=protocol_id,
            data=data,
            data_format=data_format
        )
        return ResponseModel(data=result, message="数据解析成功")
    except Exception as e:
        logger.error(f"数据解析失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{protocol_id}/validate", response_model=ResponseModel)
async def validate_protocol(
    protocol_id: str,
    protocol_service: ProtocolService = Depends(get_protocol_service)
):
    """验证协议配置"""
    try:
        result = await protocol_service.validate_protocol(protocol_id)
        return ResponseModel(data=result, message="协议验证完成")
    except Exception as e:
        logger.error(f"协议验证失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{protocol_id}/duplicate", response_model=ResponseModel)
async def duplicate_protocol(
    protocol_id: str,
    new_name: str,
    protocol_service: ProtocolService = Depends(get_protocol_service)
):
    """复制协议"""
    try:
        protocol = await protocol_service.duplicate_protocol(protocol_id, new_name)
        if not protocol:
            raise HTTPException(status_code=404, detail="源协议不存在")
        return ResponseModel(data=protocol, message="协议复制成功")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"协议复制失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{protocol_id}/export", response_model=ResponseModel)
async def export_protocol(
    protocol_id: str,
    format: str = "json",  # json, xml, csv
    protocol_service: ProtocolService = Depends(get_protocol_service)
):
    """导出协议"""
    try:
        result = await protocol_service.export_protocol(protocol_id, format)
        if not result:
            raise HTTPException(status_code=404, detail="协议不存在")
        return ResponseModel(data=result, message="协议导出成功")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"协议导出失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/import", response_model=ResponseModel)
async def import_protocol(
    data: str,
    format: str = "json",  # json, xml, csv
    protocol_service: ProtocolService = Depends(get_protocol_service)
):
    """导入协议"""
    try:
        protocol = await protocol_service.import_protocol(data, format)
        return ResponseModel(data=protocol, message="协议导入成功")
    except Exception as e:
        logger.error(f"协议导入失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{protocol_id}/fields", response_model=ResponseModel)
async def get_protocol_fields(
    protocol_id: str,
    protocol_service: ProtocolService = Depends(get_protocol_service)
):
    """获取协议字段列表"""
    try:
        fields = await protocol_service.get_protocol_fields(protocol_id)
        if fields is None:
            raise HTTPException(status_code=404, detail="协议不存在")
        return ResponseModel(data=fields, message="获取协议字段成功")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取协议字段失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{protocol_id}/fields", response_model=ResponseModel)
async def add_protocol_field(
    protocol_id: str,
    field: ProtocolField,
    protocol_service: ProtocolService = Depends(get_protocol_service)
):
    """添加协议字段"""
    try:
        success = await protocol_service.add_protocol_field(protocol_id, field)
        if not success:
            raise HTTPException(status_code=404, detail="协议不存在")
        return ResponseModel(message="协议字段添加成功")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"协议字段添加失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/{protocol_id}/fields/{field_id}", response_model=ResponseModel)
async def update_protocol_field(
    protocol_id: str,
    field_id: str,
    field: ProtocolField,
    protocol_service: ProtocolService = Depends(get_protocol_service)
):
    """更新协议字段"""
    try:
        success = await protocol_service.update_protocol_field(protocol_id, field_id, field)
        if not success:
            raise HTTPException(status_code=404, detail="协议或字段不存在")
        return ResponseModel(message="协议字段更新成功")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"协议字段更新失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{protocol_id}/fields/{field_id}", response_model=ResponseModel)
async def delete_protocol_field(
    protocol_id: str,
    field_id: str,
    protocol_service: ProtocolService = Depends(get_protocol_service)
):
    """删除协议字段"""
    try:
        success = await protocol_service.delete_protocol_field(protocol_id, field_id)
        if not success:
            raise HTTPException(status_code=404, detail="协议或字段不存在")
        return ResponseModel(message="协议字段删除成功")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"协议字段删除失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))