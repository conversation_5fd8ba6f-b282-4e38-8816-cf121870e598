import json
import os
import zipfile
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime
from loguru import logger

from backend.models.base import Project, ProjectTemplate
from backend.utils.database import get_db_manager


class ProjectService:
    """项目管理服务"""
    
    def __init__(self):
        self.db = get_db_manager()
        self.template_categories = {
            "basic": "基础模板",
            "serial": "串口通信",
            "automation": "自动化测试",
            "monitoring": "监控系统",
            "custom": "自定义"
        }
        self.template_tags = [
            "串口", "协议解析", "自动化", "测试", "监控", 
            "数据采集", "界面设计", "流程控制", "报告生成"
        ]
    
    async def get_projects(
        self, 
        page: int = 1, 
        page_size: int = 10,
        search: Optional[str] = None,
        category: Optional[str] = None,
        is_active: Optional[bool] = None
    ) -> Tuple[List[Project], int]:
        """获取项目列表"""
        try:
            return await self.db.get_projects(
                page=page, page_size=page_size, search=search,
                category=category, is_active=is_active
            )
        except Exception as e:
            logger.error(f"获取项目列表失败: {e}")
            raise
    
    async def get_project(self, project_id: str) -> Optional[Project]:
        """获取项目详情"""
        try:
            return await self.db.get_project(project_id)
        except Exception as e:
            logger.error(f"获取项目详情失败: {e}")
            raise
    
    async def create_project(
        self,
        name: str,
        description: str = "",
        category: str = "custom",
        tags: List[str] = None,
        config: Dict[str, Any] = None,
        template_id: Optional[str] = None
    ) -> Project:
        """创建项目"""
        try:
            # 如果指定了模板，从模板创建
            if template_id:
                template = await self.get_template(template_id)
                if template:
                    # 使用模板的配置
                    config = config or template.config
                    if not tags:
                        tags = template.tags
                    if category == "custom" and template.category:
                        category = template.category
            
            project_data = {
                "name": name,
                "description": description,
                "category": category,
                "tags": tags or [],
                "config": config or {},
                "template_id": template_id,
                "is_active": True,
                "created_at": datetime.now(),
                "updated_at": datetime.now()
            }
            
            project = await self.db.create_project(project_data)
            
            # 如果使用模板，复制模板内容
            if template_id and template:
                await self._copy_template_content(project.id, template)
            
            return project
            
        except Exception as e:
            logger.error(f"创建项目失败: {e}")
            raise
    
    async def _copy_template_content(self, project_id: str, template: ProjectTemplate):
        """复制模板内容到项目"""
        try:
            # 这里应该复制模板中的协议、界面、流程等内容
            # 由于涉及多个服务，这里只是示例实现
            logger.info(f"从模板 {template.name} 复制内容到项目 {project_id}")
            
            # 实际实现中，应该调用相应的服务来复制内容
            # 例如：
            # - 复制协议配置
            # - 复制界面设计
            # - 复制流程定义
            
        except Exception as e:
            logger.error(f"复制模板内容失败: {e}")
            # 不抛出异常，避免影响项目创建
    
    async def update_project(
        self, 
        project_id: str, 
        updates: Dict[str, Any]
    ) -> Optional[Project]:
        """更新项目"""
        try:
            updates["updated_at"] = datetime.now()
            return await self.db.update_project(project_id, updates)
        except Exception as e:
            logger.error(f"更新项目失败: {e}")
            raise
    
    async def delete_project(self, project_id: str) -> bool:
        """删除项目"""
        try:
            # 这里应该检查项目是否有关联的协议、界面、流程等
            # 并提供选项来决定是否一并删除
            
            return await self.db.delete_project(project_id)
        except Exception as e:
            logger.error(f"删除项目失败: {e}")
            raise
    
    async def duplicate_project(
        self, 
        project_id: str, 
        new_name: str
    ) -> Optional[Project]:
        """复制项目"""
        try:
            original = await self.get_project(project_id)
            if not original:
                return None
            
            # 创建副本
            project_data = {
                "name": new_name,
                "description": f"{original.description} (副本)",
                "category": original.category,
                "tags": original.tags,
                "config": original.config,
                "template_id": original.template_id,
                "is_active": True,
                "created_at": datetime.now(),
                "updated_at": datetime.now()
            }
            
            new_project = await self.db.create_project(project_data)
            
            # 复制项目内容
            await self._copy_project_content(project_id, new_project.id)
            
            return new_project
            
        except Exception as e:
            logger.error(f"复制项目失败: {e}")
            raise
    
    async def _copy_project_content(self, source_project_id: str, target_project_id: str):
        """复制项目内容"""
        try:
            # 这里应该复制项目中的所有内容
            # 包括协议、界面、流程等
            logger.info(f"复制项目内容从 {source_project_id} 到 {target_project_id}")
            
            # 实际实现中，应该调用相应的服务来复制内容
            
        except Exception as e:
            logger.error(f"复制项目内容失败: {e}")
    
    async def export_project(
        self, 
        project_id: str, 
        include_data: bool = True
    ) -> Optional[Dict[str, Any]]:
        """导出项目"""
        try:
            project = await self.get_project(project_id)
            if not project:
                return None
            
            export_data = {
                "project": {
                    "name": project.name,
                    "description": project.description,
                    "category": project.category,
                    "tags": project.tags,
                    "config": project.config
                },
                "export_time": datetime.now().isoformat(),
                "version": "1.0"
            }
            
            if include_data:
                # 导出项目相关的数据
                export_data["protocols"] = await self._export_project_protocols(project_id)
                export_data["interfaces"] = await self._export_project_interfaces(project_id)
                export_data["flows"] = await self._export_project_flows(project_id)
            
            # 创建ZIP文件
            zip_content = await self._create_project_zip(export_data)
            
            return {
                "content": zip_content,
                "filename": f"{project.name}_project.zip",
                "content_type": "application/zip"
            }
            
        except Exception as e:
            logger.error(f"导出项目失败: {e}")
            raise
    
    async def _export_project_protocols(self, project_id: str) -> List[Dict[str, Any]]:
        """导出项目协议"""
        # 这里应该调用协议服务来获取项目的协议
        return []
    
    async def _export_project_interfaces(self, project_id: str) -> List[Dict[str, Any]]:
        """导出项目界面"""
        # 这里应该调用界面服务来获取项目的界面
        return []
    
    async def _export_project_flows(self, project_id: str) -> List[Dict[str, Any]]:
        """导出项目流程"""
        # 这里应该调用流程服务来获取项目的流程
        return []
    
    async def _create_project_zip(self, export_data: Dict[str, Any]) -> bytes:
        """创建项目ZIP文件"""
        import io
        
        zip_buffer = io.BytesIO()
        
        with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
            # 添加项目配置文件
            zip_file.writestr(
                "project.json", 
                json.dumps(export_data, indent=2, ensure_ascii=False)
            )
            
            # 添加README文件
            readme_content = f"""
# {export_data['project']['name']}

{export_data['project']['description']}

## 项目信息
- 类别: {export_data['project']['category']}
- 标签: {', '.join(export_data['project']['tags'])}
- 导出时间: {export_data['export_time']}

## 文件说明
- project.json: 项目配置和数据
- protocols/: 协议配置文件
- interfaces/: 界面设计文件
- flows/: 流程定义文件
"""
            zip_file.writestr("README.md", readme_content)
            
            # 添加各类数据文件
            if "protocols" in export_data and export_data["protocols"]:
                for i, protocol in enumerate(export_data["protocols"]):
                    zip_file.writestr(
                        f"protocols/protocol_{i+1}.json",
                        json.dumps(protocol, indent=2, ensure_ascii=False)
                    )
            
            if "interfaces" in export_data and export_data["interfaces"]:
                for i, interface in enumerate(export_data["interfaces"]):
                    zip_file.writestr(
                        f"interfaces/interface_{i+1}.json",
                        json.dumps(interface, indent=2, ensure_ascii=False)
                    )
            
            if "flows" in export_data and export_data["flows"]:
                for i, flow in enumerate(export_data["flows"]):
                    zip_file.writestr(
                        f"flows/flow_{i+1}.json",
                        json.dumps(flow, indent=2, ensure_ascii=False)
                    )
        
        return zip_buffer.getvalue()
    
    async def import_project(
        self, 
        file_content: bytes, 
        filename: str
    ) -> Project:
        """导入项目"""
        try:
            import io
            
            # 解析ZIP文件
            with zipfile.ZipFile(io.BytesIO(file_content), 'r') as zip_file:
                # 读取项目配置
                project_config = json.loads(zip_file.read("project.json").decode('utf-8'))
                
                # 创建项目
                project_data = project_config["project"]
                project = await self.create_project(
                    name=project_data["name"],
                    description=project_data["description"],
                    category=project_data["category"],
                    tags=project_data["tags"],
                    config=project_data["config"]
                )
                
                # 导入项目数据
                await self._import_project_data(project.id, project_config, zip_file)
                
                return project
            
        except Exception as e:
            logger.error(f"导入项目失败: {e}")
            raise
    
    async def _import_project_data(
        self, 
        project_id: str, 
        project_config: Dict[str, Any], 
        zip_file: zipfile.ZipFile
    ):
        """导入项目数据"""
        try:
            # 导入协议
            if "protocols" in project_config:
                for protocol_data in project_config["protocols"]:
                    # 这里应该调用协议服务来创建协议
                    pass
            
            # 导入界面
            if "interfaces" in project_config:
                for interface_data in project_config["interfaces"]:
                    # 这里应该调用界面服务来创建界面
                    pass
            
            # 导入流程
            if "flows" in project_config:
                for flow_data in project_config["flows"]:
                    # 这里应该调用流程服务来创建流程
                    pass
            
        except Exception as e:
            logger.error(f"导入项目数据失败: {e}")
    
    async def get_project_statistics(self, project_id: str) -> Dict[str, Any]:
        """获取项目统计信息"""
        try:
            project = await self.get_project(project_id)
            if not project:
                return {}
            
            # 获取项目相关的统计信息
            stats = {
                "project_info": {
                    "name": project.name,
                    "category": project.category,
                    "created_at": project.created_at.isoformat(),
                    "updated_at": project.updated_at.isoformat()
                },
                "protocols_count": await self._count_project_protocols(project_id),
                "interfaces_count": await self._count_project_interfaces(project_id),
                "flows_count": await self._count_project_flows(project_id),
                "executions_count": await self._count_project_executions(project_id)
            }
            
            return stats
            
        except Exception as e:
            logger.error(f"获取项目统计失败: {e}")
            raise
    
    async def _count_project_protocols(self, project_id: str) -> int:
        """统计项目协议数量"""
        # 这里应该调用协议服务来统计
        return 0
    
    async def _count_project_interfaces(self, project_id: str) -> int:
        """统计项目界面数量"""
        # 这里应该调用界面服务来统计
        return 0
    
    async def _count_project_flows(self, project_id: str) -> int:
        """统计项目流程数量"""
        # 这里应该调用流程服务来统计
        return 0
    
    async def _count_project_executions(self, project_id: str) -> int:
        """统计项目执行次数"""
        # 这里应该调用流程服务来统计
        return 0
    
    # 模板管理方法
    
    async def get_templates(
        self, 
        page: int = 1, 
        page_size: int = 10,
        search: Optional[str] = None,
        category: Optional[str] = None,
        tags: List[str] = None
    ) -> Tuple[List[ProjectTemplate], int]:
        """获取模板列表"""
        try:
            return await self.db.get_templates(
                page=page, page_size=page_size, search=search,
                category=category, tags=tags
            )
        except Exception as e:
            logger.error(f"获取模板列表失败: {e}")
            raise
    
    async def get_template(self, template_id: str) -> Optional[ProjectTemplate]:
        """获取模板详情"""
        try:
            return await self.db.get_template(template_id)
        except Exception as e:
            logger.error(f"获取模板详情失败: {e}")
            raise
    
    async def create_template(
        self,
        name: str,
        description: str = "",
        category: str = "custom",
        tags: List[str] = None,
        config: Dict[str, Any] = None,
        content: Dict[str, Any] = None
    ) -> ProjectTemplate:
        """创建模板"""
        try:
            template_data = {
                "name": name,
                "description": description,
                "category": category,
                "tags": tags or [],
                "config": config or {},
                "content": content or {},
                "is_active": True,
                "created_at": datetime.now(),
                "updated_at": datetime.now()
            }
            
            return await self.db.create_template(template_data)
            
        except Exception as e:
            logger.error(f"创建模板失败: {e}")
            raise
    
    async def update_template(
        self, 
        template_id: str, 
        updates: Dict[str, Any]
    ) -> Optional[ProjectTemplate]:
        """更新模板"""
        try:
            updates["updated_at"] = datetime.now()
            return await self.db.update_template(template_id, updates)
        except Exception as e:
            logger.error(f"更新模板失败: {e}")
            raise
    
    async def delete_template(self, template_id: str) -> bool:
        """删除模板"""
        try:
            return await self.db.delete_template(template_id)
        except Exception as e:
            logger.error(f"删除模板失败: {e}")
            raise
    
    async def duplicate_template(
        self, 
        template_id: str, 
        new_name: str
    ) -> Optional[ProjectTemplate]:
        """复制模板"""
        try:
            original = await self.get_template(template_id)
            if not original:
                return None
            
            # 创建副本
            template_data = {
                "name": new_name,
                "description": f"{original.description} (副本)",
                "category": original.category,
                "tags": original.tags,
                "config": original.config,
                "content": original.content,
                "is_active": True,
                "created_at": datetime.now(),
                "updated_at": datetime.now()
            }
            
            return await self.db.create_template(template_data)
            
        except Exception as e:
            logger.error(f"复制模板失败: {e}")
            raise
    
    async def create_template_from_project(
        self, 
        project_id: str, 
        template_name: str,
        template_description: str = ""
    ) -> Optional[ProjectTemplate]:
        """从项目创建模板"""
        try:
            project = await self.get_project(project_id)
            if not project:
                return None
            
            # 收集项目内容
            content = {
                "protocols": await self._export_project_protocols(project_id),
                "interfaces": await self._export_project_interfaces(project_id),
                "flows": await self._export_project_flows(project_id)
            }
            
            # 创建模板
            return await self.create_template(
                name=template_name,
                description=template_description or f"从项目 {project.name} 创建的模板",
                category=project.category,
                tags=project.tags,
                config=project.config,
                content=content
            )
            
        except Exception as e:
            logger.error(f"从项目创建模板失败: {e}")
            raise
    
    async def get_template_categories(self) -> Dict[str, str]:
        """获取模板分类"""
        return self.template_categories
    
    async def get_template_tags(self) -> List[str]:
        """获取模板标签"""
        return self.template_tags
    
    async def get_global_statistics(self) -> Dict[str, Any]:
        """获取全局统计信息"""
        try:
            return await self.db.get_global_statistics()
        except Exception as e:
            logger.error(f"获取全局统计失败: {e}")
            raise