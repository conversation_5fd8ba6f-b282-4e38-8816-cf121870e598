from typing import List, Optional
from fastapi import APIRouter, HTTPException, Depends, UploadFile, File
from pydantic import BaseModel
from loguru import logger

from backend.models.base import (
    ResponseModel, Project, ProjectTemplate, PaginationModel
)
from backend.services.project_service import ProjectService

router = APIRouter()

# 获取项目服务实例
def get_project_service() -> ProjectService:
    return ProjectService()


class ProjectCreateRequest(BaseModel):
    """项目创建请求模型"""
    name: str
    description: str = ""
    template_id: Optional[str] = None
    tags: List[str] = []
    config: dict = {}


class ProjectUpdateRequest(BaseModel):
    """项目更新请求模型"""
    name: Optional[str] = None
    description: Optional[str] = None
    tags: Optional[List[str]] = None
    config: Optional[dict] = None
    is_active: Optional[bool] = None


class TemplateCreateRequest(BaseModel):
    """模板创建请求模型"""
    name: str
    description: str = ""
    category: str = "custom"
    tags: List[str] = []
    config: dict = {}
    preview_image: Optional[str] = None


class TemplateUpdateRequest(BaseModel):
    """模板更新请求模型"""
    name: Optional[str] = None
    description: Optional[str] = None
    category: Optional[str] = None
    tags: Optional[List[str]] = None
    config: Optional[dict] = None
    preview_image: Optional[str] = None
    is_active: Optional[bool] = None


# 项目管理路由
@router.get("/", response_model=ResponseModel)
async def get_projects(
    page: int = 1,
    page_size: int = 10,
    search: Optional[str] = None,
    tags: Optional[str] = None,
    is_active: Optional[bool] = None,
    project_service: ProjectService = Depends(get_project_service)
):
    """获取项目列表"""
    try:
        tag_list = tags.split(",") if tags else None
        projects, total = await project_service.get_projects(
            page=page, page_size=page_size, search=search, 
            tags=tag_list, is_active=is_active
        )
        pagination = PaginationModel(
            page=page,
            page_size=page_size,
            total=total,
            total_pages=(total + page_size - 1) // page_size
        )
        return ResponseModel(
            data={"projects": projects, "pagination": pagination},
            message="获取项目列表成功"
        )
    except Exception as e:
        logger.error(f"获取项目列表失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{project_id}", response_model=ResponseModel)
async def get_project(
    project_id: str,
    project_service: ProjectService = Depends(get_project_service)
):
    """获取项目详情"""
    try:
        project = await project_service.get_project(project_id)
        if not project:
            raise HTTPException(status_code=404, detail="项目不存在")
        return ResponseModel(data=project, message="获取项目详情成功")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取项目详情失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/", response_model=ResponseModel)
async def create_project(
    request: ProjectCreateRequest,
    project_service: ProjectService = Depends(get_project_service)
):
    """创建项目"""
    try:
        project = await project_service.create_project(
            name=request.name,
            description=request.description,
            template_id=request.template_id,
            tags=request.tags,
            config=request.config
        )
        return ResponseModel(data=project, message="项目创建成功")
    except Exception as e:
        logger.error(f"项目创建失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/{project_id}", response_model=ResponseModel)
async def update_project(
    project_id: str,
    request: ProjectUpdateRequest,
    project_service: ProjectService = Depends(get_project_service)
):
    """更新项目"""
    try:
        project = await project_service.update_project(
            project_id, request.dict(exclude_unset=True)
        )
        if not project:
            raise HTTPException(status_code=404, detail="项目不存在")
        return ResponseModel(data=project, message="项目更新成功")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"项目更新失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{project_id}", response_model=ResponseModel)
async def delete_project(
    project_id: str,
    project_service: ProjectService = Depends(get_project_service)
):
    """删除项目"""
    try:
        success = await project_service.delete_project(project_id)
        if not success:
            raise HTTPException(status_code=404, detail="项目不存在")
        return ResponseModel(message="项目删除成功")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"项目删除失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{project_id}/duplicate", response_model=ResponseModel)
async def duplicate_project(
    project_id: str,
    new_name: str,
    project_service: ProjectService = Depends(get_project_service)
):
    """复制项目"""
    try:
        project = await project_service.duplicate_project(project_id, new_name)
        if not project:
            raise HTTPException(status_code=404, detail="源项目不存在")
        return ResponseModel(data=project, message="项目复制成功")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"项目复制失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{project_id}/export", response_model=ResponseModel)
async def export_project(
    project_id: str,
    format: str = "json",  # json, zip
    include_data: bool = True,
    project_service: ProjectService = Depends(get_project_service)
):
    """导出项目"""
    try:
        result = await project_service.export_project(
            project_id, format, include_data
        )
        if not result:
            raise HTTPException(status_code=404, detail="项目不存在")
        return ResponseModel(data=result, message="项目导出成功")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"项目导出失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/import", response_model=ResponseModel)
async def import_project(
    file: UploadFile = File(...),
    project_service: ProjectService = Depends(get_project_service)
):
    """导入项目"""
    try:
        content = await file.read()
        project = await project_service.import_project(content, file.filename)
        return ResponseModel(data=project, message="项目导入成功")
    except Exception as e:
        logger.error(f"项目导入失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{project_id}/statistics", response_model=ResponseModel)
async def get_project_statistics(
    project_id: str,
    project_service: ProjectService = Depends(get_project_service)
):
    """获取项目统计信息"""
    try:
        stats = await project_service.get_project_statistics(project_id)
        if not stats:
            raise HTTPException(status_code=404, detail="项目不存在")
        return ResponseModel(data=stats, message="获取项目统计成功")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取项目统计失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# 模板管理路由
@router.get("/templates/", response_model=ResponseModel)
async def get_templates(
    page: int = 1,
    page_size: int = 10,
    search: Optional[str] = None,
    category: Optional[str] = None,
    tags: Optional[str] = None,
    is_active: Optional[bool] = None,
    project_service: ProjectService = Depends(get_project_service)
):
    """获取模板列表"""
    try:
        tag_list = tags.split(",") if tags else None
        templates, total = await project_service.get_templates(
            page=page, page_size=page_size, search=search,
            category=category, tags=tag_list, is_active=is_active
        )
        pagination = PaginationModel(
            page=page,
            page_size=page_size,
            total=total,
            total_pages=(total + page_size - 1) // page_size
        )
        return ResponseModel(
            data={"templates": templates, "pagination": pagination},
            message="获取模板列表成功"
        )
    except Exception as e:
        logger.error(f"获取模板列表失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/templates/{template_id}", response_model=ResponseModel)
async def get_template(
    template_id: str,
    project_service: ProjectService = Depends(get_project_service)
):
    """获取模板详情"""
    try:
        template = await project_service.get_template(template_id)
        if not template:
            raise HTTPException(status_code=404, detail="模板不存在")
        return ResponseModel(data=template, message="获取模板详情成功")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取模板详情失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/templates/", response_model=ResponseModel)
async def create_template(
    request: TemplateCreateRequest,
    project_service: ProjectService = Depends(get_project_service)
):
    """创建模板"""
    try:
        template = await project_service.create_template(
            name=request.name,
            description=request.description,
            category=request.category,
            tags=request.tags,
            config=request.config,
            preview_image=request.preview_image
        )
        return ResponseModel(data=template, message="模板创建成功")
    except Exception as e:
        logger.error(f"模板创建失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/templates/{template_id}", response_model=ResponseModel)
async def update_template(
    template_id: str,
    request: TemplateUpdateRequest,
    project_service: ProjectService = Depends(get_project_service)
):
    """更新模板"""
    try:
        template = await project_service.update_template(
            template_id, request.dict(exclude_unset=True)
        )
        if not template:
            raise HTTPException(status_code=404, detail="模板不存在")
        return ResponseModel(data=template, message="模板更新成功")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"模板更新失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/templates/{template_id}", response_model=ResponseModel)
async def delete_template(
    template_id: str,
    project_service: ProjectService = Depends(get_project_service)
):
    """删除模板"""
    try:
        success = await project_service.delete_template(template_id)
        if not success:
            raise HTTPException(status_code=404, detail="模板不存在")
        return ResponseModel(message="模板删除成功")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"模板删除失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/templates/{template_id}/duplicate", response_model=ResponseModel)
async def duplicate_template(
    template_id: str,
    new_name: str,
    project_service: ProjectService = Depends(get_project_service)
):
    """复制模板"""
    try:
        template = await project_service.duplicate_template(template_id, new_name)
        if not template:
            raise HTTPException(status_code=404, detail="源模板不存在")
        return ResponseModel(data=template, message="模板复制成功")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"模板复制失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/templates/categories", response_model=ResponseModel)
async def get_template_categories(
    project_service: ProjectService = Depends(get_project_service)
):
    """获取模板分类"""
    try:
        categories = await project_service.get_template_categories()
        return ResponseModel(data=categories, message="获取模板分类成功")
    except Exception as e:
        logger.error(f"获取模板分类失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tags", response_model=ResponseModel)
async def get_project_tags(
    project_service: ProjectService = Depends(get_project_service)
):
    """获取项目标签"""
    try:
        tags = await project_service.get_project_tags()
        return ResponseModel(data=tags, message="获取项目标签成功")
    except Exception as e:
        logger.error(f"获取项目标签失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/statistics", response_model=ResponseModel)
async def get_projects_statistics(
    project_service: ProjectService = Depends(get_project_service)
):
    """获取项目统计信息"""
    try:
        stats = await project_service.get_projects_statistics()
        return ResponseModel(data=stats, message="获取项目统计成功")
    except Exception as e:
        logger.error(f"获取项目统计失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))