version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: serial-communication-tool
    restart: unless-stopped
    ports:
      - "8000:8000"
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./uploads:/app/uploads
      - ./backups:/app/backups
    environment:
      - ENVIRONMENT=production
      - DEBUG=false
      - HOST=0.0.0.0
      - PORT=8000
      - LOG_LEVEL=INFO
      - DATABASE_URL=sqlite:///./data/app.db
      - SECRET_KEY=${SECRET_KEY:-your-secret-key-change-in-production}
    devices:
      # 串口设备映射 (取消注释并根据需要修改)
      # - /dev/ttyUSB0:/dev/ttyUSB0
      # - /dev/ttyACM0:/dev/ttyACM0
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

  # 可选: 添加反向代理服务
  # nginx:
  #   image: nginx:alpine
  #   container_name: serial-communication-tool-nginx
  #   restart: unless-stopped
  #   ports:
  #     - "80:80"
  #     - "443:443"
  #   volumes:
  #     - ./nginx/conf.d:/etc/nginx/conf.d
  #     - ./nginx/ssl:/etc/nginx/ssl
  #   depends_on:
  #     - app

volumes:
  data:
  logs:
  uploads:
  backups:

networks:
  default:
    driver: bridge