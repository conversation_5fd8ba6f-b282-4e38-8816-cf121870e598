import json
import asyncio
from typing import Dict, List, Set, Any, Optional
from datetime import datetime
from fastapi import WebSocket, WebSocketDisconnect
from loguru import logger


class WebSocketManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        # 活跃连接
        self.active_connections: List[WebSocket] = []
        # 连接ID映射
        self.connection_ids: Dict[WebSocket, str] = {}
        # 订阅主题
        self.subscriptions: Dict[str, Set[WebSocket]] = {}
        # 心跳任务
        self.heartbeat_task: Optional[asyncio.Task] = None
        # 连接统计
        self.connection_stats = {
            "total_connections": 0,
            "current_connections": 0,
            "messages_sent": 0,
            "messages_received": 0
        }
    
    async def startup(self):
        """启动WebSocket管理器"""
        logger.info("启动WebSocket管理器")
        # 启动心跳任务
        self.heartbeat_task = asyncio.create_task(self._heartbeat_loop())
    
    async def shutdown(self):
        """关闭WebSocket管理器"""
        logger.info("关闭WebSocket管理器")
        
        # 取消心跳任务
        if self.heartbeat_task:
            self.heartbeat_task.cancel()
            try:
                await self.heartbeat_task
            except asyncio.CancelledError:
                pass
        
        # 关闭所有连接
        for connection in self.active_connections.copy():
            await self.disconnect(connection)
    
    async def connect(self, websocket: WebSocket, connection_id: str = None) -> str:
        """接受WebSocket连接
        
        Args:
            websocket: WebSocket连接
            connection_id: 连接ID，如果不提供则自动生成
            
        Returns:
            连接ID
        """
        await websocket.accept()
        
        # 生成连接ID
        if not connection_id:
            connection_id = f"conn_{len(self.active_connections)}_{datetime.now().timestamp()}"
        
        # 添加到活跃连接
        self.active_connections.append(websocket)
        self.connection_ids[websocket] = connection_id
        
        # 更新统计
        self.connection_stats["total_connections"] += 1
        self.connection_stats["current_connections"] = len(self.active_connections)
        
        logger.info(f"WebSocket连接已建立: {connection_id}")
        
        # 发送欢迎消息
        await self.send_personal_message(websocket, {
            "type": "connection_established",
            "connection_id": connection_id,
            "timestamp": datetime.now().isoformat()
        })
        
        return connection_id
    
    async def disconnect(self, websocket: WebSocket):
        """断开WebSocket连接"""
        if websocket in self.active_connections:
            connection_id = self.connection_ids.get(websocket, "unknown")
            
            # 从活跃连接中移除
            self.active_connections.remove(websocket)
            if websocket in self.connection_ids:
                del self.connection_ids[websocket]
            
            # 从所有订阅中移除
            for topic_connections in self.subscriptions.values():
                topic_connections.discard(websocket)
            
            # 更新统计
            self.connection_stats["current_connections"] = len(self.active_connections)
            
            logger.info(f"WebSocket连接已断开: {connection_id}")
            
            try:
                await websocket.close()
            except Exception as e:
                logger.warning(f"关闭WebSocket连接时出错: {e}")
    
    async def send_personal_message(self, websocket: WebSocket, message: Dict[str, Any]):
        """发送个人消息"""
        try:
            await websocket.send_text(json.dumps(message, ensure_ascii=False))
            self.connection_stats["messages_sent"] += 1
        except Exception as e:
            logger.error(f"发送个人消息失败: {e}")
            await self.disconnect(websocket)
    
    async def broadcast(self, message: Dict[str, Any], exclude: Set[WebSocket] = None):
        """广播消息给所有连接"""
        if exclude is None:
            exclude = set()
        
        message_text = json.dumps(message, ensure_ascii=False)
        disconnected = []
        
        for connection in self.active_connections:
            if connection not in exclude:
                try:
                    await connection.send_text(message_text)
                    self.connection_stats["messages_sent"] += 1
                except Exception as e:
                    logger.error(f"广播消息失败: {e}")
                    disconnected.append(connection)
        
        # 清理断开的连接
        for connection in disconnected:
            await self.disconnect(connection)
    
    async def subscribe(self, websocket: WebSocket, topic: str):
        """订阅主题"""
        if topic not in self.subscriptions:
            self.subscriptions[topic] = set()
        
        self.subscriptions[topic].add(websocket)
        connection_id = self.connection_ids.get(websocket, "unknown")
        logger.info(f"连接 {connection_id} 订阅主题: {topic}")
        
        # 发送订阅确认
        await self.send_personal_message(websocket, {
            "type": "subscription_confirmed",
            "topic": topic,
            "timestamp": datetime.now().isoformat()
        })
    
    async def unsubscribe(self, websocket: WebSocket, topic: str):
        """取消订阅主题"""
        if topic in self.subscriptions:
            self.subscriptions[topic].discard(websocket)
            connection_id = self.connection_ids.get(websocket, "unknown")
            logger.info(f"连接 {connection_id} 取消订阅主题: {topic}")
            
            # 发送取消订阅确认
            await self.send_personal_message(websocket, {
                "type": "unsubscription_confirmed",
                "topic": topic,
                "timestamp": datetime.now().isoformat()
            })
    
    async def publish_to_topic(self, topic: str, message: Dict[str, Any]):
        """发布消息到主题"""
        if topic not in self.subscriptions:
            return
        
        message_with_topic = {
            "topic": topic,
            "timestamp": datetime.now().isoformat(),
            **message
        }
        
        message_text = json.dumps(message_with_topic, ensure_ascii=False)
        disconnected = []
        
        for connection in self.subscriptions[topic].copy():
            try:
                await connection.send_text(message_text)
                self.connection_stats["messages_sent"] += 1
            except Exception as e:
                logger.error(f"发布主题消息失败: {e}")
                disconnected.append(connection)
        
        # 清理断开的连接
        for connection in disconnected:
            await self.disconnect(connection)
    
    async def handle_message(self, websocket: WebSocket, data: Dict[str, Any]):
        """处理接收到的消息"""
        self.connection_stats["messages_received"] += 1
        connection_id = self.connection_ids.get(websocket, "unknown")
        
        try:
            message_type = data.get("type")
            
            if message_type == "ping":
                # 心跳响应
                await self.send_personal_message(websocket, {
                    "type": "pong",
                    "timestamp": datetime.now().isoformat()
                })
            
            elif message_type == "subscribe":
                # 订阅主题
                topic = data.get("topic")
                if topic:
                    await self.subscribe(websocket, topic)
            
            elif message_type == "unsubscribe":
                # 取消订阅
                topic = data.get("topic")
                if topic:
                    await self.unsubscribe(websocket, topic)
            
            elif message_type == "get_stats":
                # 获取统计信息
                await self.send_personal_message(websocket, {
                    "type": "stats",
                    "data": self.get_stats(),
                    "timestamp": datetime.now().isoformat()
                })
            
            else:
                logger.warning(f"未知消息类型: {message_type} from {connection_id}")
                await self.send_personal_message(websocket, {
                    "type": "error",
                    "message": f"未知消息类型: {message_type}",
                    "timestamp": datetime.now().isoformat()
                })
        
        except Exception as e:
            logger.error(f"处理WebSocket消息失败: {e}")
            await self.send_personal_message(websocket, {
                "type": "error",
                "message": "消息处理失败",
                "timestamp": datetime.now().isoformat()
            })
    
    async def _heartbeat_loop(self):
        """心跳循环"""
        while True:
            try:
                await asyncio.sleep(30)  # 30秒心跳间隔
                
                if self.active_connections:
                    await self.broadcast({
                        "type": "heartbeat",
                        "timestamp": datetime.now().isoformat(),
                        "active_connections": len(self.active_connections)
                    })
            
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"心跳循环出错: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            **self.connection_stats,
            "subscriptions": {topic: len(connections) for topic, connections in self.subscriptions.items()},
            "topics": list(self.subscriptions.keys())
        }
    
    def get_connection_info(self, websocket: WebSocket) -> Optional[Dict[str, Any]]:
        """获取连接信息"""
        if websocket not in self.active_connections:
            return None
        
        connection_id = self.connection_ids.get(websocket, "unknown")
        subscribed_topics = [topic for topic, connections in self.subscriptions.items() if websocket in connections]
        
        return {
            "connection_id": connection_id,
            "subscribed_topics": subscribed_topics,
            "connected_at": datetime.now().isoformat()  # 这里应该记录实际连接时间
        }


# 全局WebSocket管理器实例
websocket_manager = WebSocketManager()