// 串口相关类型
export interface SerialConfig {
  baudRate: number
  dataBits: 7 | 8
  stopBits: 1 | 2
  parity: 'none' | 'even' | 'odd'
  flowControl?: boolean
  rtscts?: boolean
  xon?: boolean
  xoff?: boolean
  xany?: boolean
}

export interface SerialData {
  id: string
  timestamp: string
  data: string
  direction: 'send' | 'receive'
  format: 'hex' | 'ascii'
}

export interface SerialStatus {
  isConnected: boolean
  port?: string
  config?: SerialConfig
  error?: string
}

// 协议相关类型
export interface ProtocolField {
  id: string
  name: string
  dataType: 'uint8' | 'uint16' | 'uint32' | 'int8' | 'int16' | 'int32' | 'float' | 'double' | 'string' | 'bool' | 'custom'
  offset: number
  length: number
  scale?: number
  unit?: string
  description?: string
  isLittleEndian?: boolean
  customParser?: string
}

export interface Protocol {
  id: string
  name: string
  description?: string
  totalLength: number
  header?: string
  footer?: string
  fields: ProtocolField[]
  createdAt: string
  updatedAt: string
}

// 界面设计相关类型
export interface WidgetConfig {
  id: string
  type: 'text' | 'number' | 'chart' | 'gauge' | 'button' | 'switch' | 'slider' | 'select' | 'image' | 'container'
  title: string
  x: number
  y: number
  width: number
  height: number
  dataBinding?: string
  properties: Record<string, any>
  style?: Record<string, any>
  events?: WidgetEvent[]
}

export interface WidgetEvent {
  id: string
  trigger: 'click' | 'change' | 'hover' | 'timer'
  action: 'send_command' | 'set_value' | 'toggle_widget' | 'navigate' | 'custom'
  parameters: Record<string, any>
}

export interface InterfaceDesign {
  id: string
  name: string
  description?: string
  width: number
  height: number
  widgets: WidgetConfig[]
  createdAt: string
  updatedAt: string
}

// 流程引擎相关类型
export interface FlowNode {
  id: string
  type: 'start' | 'end' | 'condition' | 'action' | 'delay' | 'loop' | 'custom'
  label: string
  description?: string
  x: number
  y: number
  config: Record<string, any>
}

export interface FlowEdge {
  id: string
  source: string
  target: string
  label?: string
  condition?: string
}

export interface FlowDesign {
  id: string
  name: string
  description?: string
  nodes: FlowNode[]
  edges: FlowEdge[]
  createdAt: string
  updatedAt: string
}

export interface FlowExecutionStatus {
  id: string
  flowId: string
  status: 'idle' | 'running' | 'paused' | 'completed' | 'error'
  currentNodeId?: string
  startTime?: string
  endTime?: string
  error?: string
  variables: Record<string, any>
}

export interface FlowExecutionLog {
  id: string
  flowId: string
  executionId: string
  timestamp: string
  nodeId: string
  nodeName: string
  type: 'info' | 'warning' | 'error' | 'success'
  message: string
  data?: any
}

// 项目管理相关类型
export interface Project {
  id: string
  name: string
  description?: string
  type: 'protocol' | 'interface' | 'flow' | 'integrated'
  status: 'draft' | 'published'
  createdAt: string
  updatedAt: string
  protocols?: Protocol[]
  interfaces?: InterfaceDesign[]
  flows?: FlowDesign[]
  config?: Record<string, any>
}

export interface Template {
  id: string
  name: string
  description?: string
  type: 'protocol' | 'interface' | 'flow'
  tags: string[]
  createdAt: string
  content: any
}

// 系统相关类型
export interface SystemStatus {
  cpuUsage: number
  memoryUsage: number
  diskUsage: number
  uptime: number
  version: string
}

export interface Notification {
  id: string
  type: 'info' | 'success' | 'warning' | 'error'
  title: string
  message: string
  timestamp: string
  read: boolean
}

// 用户相关类型
export interface User {
  id: string
  username: string
  email?: string
  role: 'admin' | 'user' | 'guest'
  createdAt: string
  lastLogin?: string
}

// 通用响应类型
export interface ApiResponse<T> {
  success: boolean
  data?: T
  error?: string
  message?: string
}