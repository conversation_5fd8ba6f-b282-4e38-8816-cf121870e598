import os
from typing import List
from pathlib import Path
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用程序配置"""
    
    # 应用基本信息
    APP_NAME: str = "串口通信测试工具"
    VERSION: str = "1.0.0"
    DESCRIPTION: str = "面向非程序员的串口通信测试和协议配置平台"
    
    # 环境配置
    ENVIRONMENT: str = "development"
    DEBUG: bool = True
    
    # 服务器配置
    HOST: str = "127.0.0.1"
    PORT: int = 8000
    
    # 数据库配置
    DATABASE_URL: str = "sqlite:///./data/app.db"
    DATABASE_ECHO: bool = False
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "logs/app.log"
    LOG_MAX_SIZE: str = "10 MB"
    LOG_BACKUP_COUNT: int = 7
    LOG_FORMAT: str = "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name} | {message}"
    
    # CORS配置
    ALLOWED_ORIGINS: List[str] = ["*"]
    ALLOWED_METHODS: List[str] = ["*"]
    ALLOWED_HEADERS: List[str] = ["*"]
    
    # 串口配置
    SERIAL_TIMEOUT: float = 1.0
    SERIAL_MAX_HISTORY: int = 10000
    SERIAL_BUFFER_SIZE: int = 4096
    
    # WebSocket配置
    WS_HEARTBEAT_INTERVAL: int = 30
    WS_MAX_CONNECTIONS: int = 100
    
    # 文件上传配置
    UPLOAD_MAX_SIZE: int = 10 * 1024 * 1024  # 10MB
    UPLOAD_ALLOWED_EXTENSIONS: List[str] = [".json", ".xml", ".csv", ".txt"]
    UPLOAD_DIR: str = "uploads"
    
    # 安全配置
    SECRET_KEY: str = "your-secret-key-change-in-production"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # 项目路径
    PROJECT_ROOT: Path = Path(__file__).parent.parent.parent
    DATA_DIR: Path = PROJECT_ROOT / "data"
    LOGS_DIR: Path = PROJECT_ROOT / "logs"
    UPLOADS_DIR: Path = PROJECT_ROOT / "uploads"
    FRONTEND_DIR: Path = PROJECT_ROOT / "frontend" / "dist"
    
    # 备份配置
    BACKUP_ENABLED: bool = True
    BACKUP_INTERVAL_HOURS: int = 24
    BACKUP_KEEP_DAYS: int = 30
    BACKUP_DIR: str = "backups"
    
    # 性能配置
    MAX_CONCURRENT_FLOWS: int = 10
    FLOW_EXECUTION_TIMEOUT: int = 300  # 5分钟
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        
        # 确保必要的目录存在
        self.DATA_DIR.mkdir(exist_ok=True)
        self.LOGS_DIR.mkdir(exist_ok=True)
        self.UPLOADS_DIR.mkdir(exist_ok=True)
        
        # 根据环境调整配置
        if self.ENVIRONMENT == "production":
            self.DEBUG = False
            self.LOG_LEVEL = "WARNING"
            self.ALLOWED_ORIGINS = ["http://localhost:3000"]  # 生产环境应该限制域名
        elif self.ENVIRONMENT == "testing":
            self.DATABASE_URL = "sqlite:///./test.db"
            self.LOG_LEVEL = "DEBUG"
    
    @property
    def database_path(self) -> Path:
        """获取数据库文件路径"""
        if self.DATABASE_URL.startswith("sqlite:///"):
            db_path = self.DATABASE_URL.replace("sqlite:///", "")
            if db_path.startswith("./"):
                return self.PROJECT_ROOT / db_path[2:]
            return Path(db_path)
        return self.DATA_DIR / "app.db"
    
    @property
    def is_development(self) -> bool:
        """是否为开发环境"""
        return self.ENVIRONMENT == "development"
    
    @property
    def is_production(self) -> bool:
        """是否为生产环境"""
        return self.ENVIRONMENT == "production"
    
    @property
    def is_testing(self) -> bool:
        """是否为测试环境"""
        return self.ENVIRONMENT == "testing"


# 创建全局配置实例
settings = Settings()


# 开发环境配置
def get_development_settings() -> Settings:
    """获取开发环境配置"""
    return Settings(
        ENVIRONMENT="development",
        DEBUG=True,
        LOG_LEVEL="DEBUG",
        HOST="0.0.0.0",
        PORT=8000
    )


# 生产环境配置
def get_production_settings() -> Settings:
    """获取生产环境配置"""
    return Settings(
        ENVIRONMENT="production",
        DEBUG=False,
        LOG_LEVEL="INFO",
        HOST="0.0.0.0",
        PORT=8000,
        ALLOWED_ORIGINS=["http://localhost:3000", "http://127.0.0.1:3000"]
    )


# 测试环境配置
def get_testing_settings() -> Settings:
    """获取测试环境配置"""
    return Settings(
        ENVIRONMENT="testing",
        DEBUG=True,
        LOG_LEVEL="DEBUG",
        DATABASE_URL="sqlite:///./test.db",
        HOST="127.0.0.1",
        PORT=8001
    )