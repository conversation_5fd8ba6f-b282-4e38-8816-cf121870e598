version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: serial-communication-tool-dev
    restart: unless-stopped
    ports:
      - "8000:8000"
    volumes:
      - .:/app
      - /app/backend/__pycache__
      - /app/frontend/node_modules
    environment:
      - ENVIRONMENT=development
      - DEBUG=true
      - HOST=0.0.0.0
      - PORT=8000
      - LOG_LEVEL=DEBUG
      - DATABASE_URL=sqlite:///./data/app.db
      - SECRET_KEY=dev-secret-key
    devices:
      # 串口设备映射 (取消注释并根据需要修改)
      # - /dev/ttyUSB0:/dev/ttyUSB0
      # - /dev/ttyACM0:/dev/ttyACM0
    command: python start.py --env development --reload

  # 前端开发服务器
  frontend:
    image: node:18-alpine
    container_name: serial-communication-tool-frontend-dev
    working_dir: /app
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    command: sh -c "npm install && npm run dev"
    environment:
      - NODE_ENV=development
      - VITE_API_URL=http://localhost:8000

volumes:
  data:
  logs:
  uploads:
  backups:

networks:
  default:
    driver: bridge