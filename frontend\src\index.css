* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 拖拽相关样式 */
.dragging {
  opacity: 0.5;
  transform: rotate(5deg);
}

.drop-zone {
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  background-color: #fafafa;
  transition: all 0.3s ease;
}

.drop-zone.active {
  border-color: #1890ff;
  background-color: #e6f7ff;
}

/* 流程图相关样式 */
.react-flow__node {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.react-flow__edge {
  stroke-width: 2;
}

/* 网格布局样式 */
.react-grid-item {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.react-grid-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.react-grid-item.react-grid-placeholder {
  background: #1890ff;
  opacity: 0.2;
  border: none;
}

/* 工具栏样式 */
.toolbar {
  background: white;
  border-bottom: 1px solid #f0f0f0;
  padding: 12px 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

/* 属性面板样式 */
.property-panel {
  background: white;
  border-left: 1px solid #f0f0f0;
  height: 100%;
  overflow-y: auto;
}

/* 状态指示器 */
.status-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
}

.status-indicator.connected {
  background-color: #52c41a;
}

.status-indicator.disconnected {
  background-color: #ff4d4f;
}

.status-indicator.connecting {
  background-color: #faad14;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ant-layout-sider {
    position: fixed !important;
    z-index: 1000;
    height: 100vh;
  }
  
  .ant-layout-content {
    margin-left: 0 !important;
  }
}