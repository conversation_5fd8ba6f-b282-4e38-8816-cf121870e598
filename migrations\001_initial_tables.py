"""
初始数据库表结构创建
创建项目、协议、界面、流程等核心表
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.utils.database import get_database_connection


def upgrade():
    """应用迁移 - 创建初始表结构"""
    with get_database_connection() as conn:
        cursor = conn.cursor()
        
        # 创建项目表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS projects (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                description TEXT DEFAULT '',
                status TEXT DEFAULT 'active',
                config TEXT DEFAULT '{}',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_by TEXT
            )
        """)
        
        # 创建协议表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS protocols (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                description TEXT DEFAULT '',
                category TEXT DEFAULT 'custom',
                project_id TEXT,
                fields TEXT DEFAULT '[]',
                config TEXT DEFAULT '{}',
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (project_id) REFERENCES projects (id) ON DELETE CASCADE
            )
        """)
        
        # 创建界面设计表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS interfaces (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                description TEXT DEFAULT '',
                project_id TEXT,
                components TEXT DEFAULT '[]',
                layout TEXT DEFAULT '{}',
                config TEXT DEFAULT '{}',
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (project_id) REFERENCES projects (id) ON DELETE CASCADE
            )
        """)
        
        # 创建流程表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS flows (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                description TEXT DEFAULT '',
                project_id TEXT,
                nodes TEXT DEFAULT '[]',
                edges TEXT DEFAULT '[]',
                variables TEXT DEFAULT '{}',
                config TEXT DEFAULT '{}',
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (project_id) REFERENCES projects (id) ON DELETE CASCADE
            )
        """)
        
        # 创建流程执行记录表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS flow_executions (
                id TEXT PRIMARY KEY,
                flow_id TEXT NOT NULL,
                status TEXT DEFAULT 'pending',
                input_data TEXT DEFAULT '{}',
                output_data TEXT DEFAULT '{}',
                variables TEXT DEFAULT '{}',
                current_node TEXT,
                start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                end_time TIMESTAMP,
                logs TEXT DEFAULT '[]',
                error_message TEXT,
                FOREIGN KEY (flow_id) REFERENCES flows (id) ON DELETE CASCADE
            )
        """)
        
        # 创建串口数据历史表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS serial_data_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                direction TEXT NOT NULL,
                data TEXT NOT NULL,
                format TEXT DEFAULT 'ascii',
                port TEXT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                bytes_count INTEGER DEFAULT 0,
                project_id TEXT,
                FOREIGN KEY (project_id) REFERENCES projects (id) ON DELETE SET NULL
            )
        """)
        
        # 创建项目模板表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS project_templates (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                description TEXT DEFAULT '',
                category TEXT DEFAULT 'custom',
                tags TEXT DEFAULT '[]',
                config TEXT DEFAULT '{}',
                template_data TEXT DEFAULT '{}',
                preview_image TEXT,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 创建系统配置表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS system_config (
                key TEXT PRIMARY KEY,
                value TEXT NOT NULL,
                description TEXT DEFAULT '',
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 创建索引
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_projects_status ON projects (status)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_protocols_project_id ON protocols (project_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_protocols_category ON protocols (category)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_interfaces_project_id ON interfaces (project_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_flows_project_id ON flows (project_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_flow_executions_flow_id ON flow_executions (flow_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_flow_executions_status ON flow_executions (status)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_serial_data_timestamp ON serial_data_history (timestamp)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_serial_data_project_id ON serial_data_history (project_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_templates_category ON project_templates (category)")
        
        conn.commit()


def downgrade():
    """回滚迁移 - 删除所有表"""
    with get_database_connection() as conn:
        cursor = conn.cursor()
        
        # 删除表（注意外键依赖顺序）
        tables = [
            'serial_data_history',
            'flow_executions',
            'flows',
            'interfaces',
            'protocols',
            'projects',
            'project_templates',
            'system_config'
        ]
        
        for table in tables:
            cursor.execute(f"DROP TABLE IF EXISTS {table}")
        
        conn.commit()
