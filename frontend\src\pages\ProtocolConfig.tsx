import React, { useState } from 'react'
import {
  Card,
  Form,
  Input,
  Select,
  InputNumber,
  Button,
  Table,
  Space,
  Modal,
  message,
  Tabs,
  Row,
  Col,
  Divider,
} from 'antd'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SaveOutlined,
  ImportOutlined,
  ExportOutlined,
} from '@ant-design/icons'

const { Option } = Select
const { TabPane } = Tabs

interface ProtocolField {
  id: string
  name: string
  offset: number
  length: number
  type: 'int8' | 'uint8' | 'int16' | 'uint16' | 'int32' | 'uint32' | 'float' | 'double' | 'string'
  scale: number
  unit: string
  description: string
}

interface ProtocolConfig {
  name: string
  frameHeader: string
  frameFooter: string
  totalLength: number
  fields: ProtocolField[]
}

const ProtocolConfig: React.FC = () => {
  const [form] = Form.useForm()
  const [fieldForm] = Form.useForm()
  const [protocol, setProtocol] = useState<ProtocolConfig>({
    name: '默认协议',
    frameHeader: 'AA55',
    frameFooter: '55AA',
    totalLength: 16,
    fields: [],
  })
  const [isFieldModalVisible, setIsFieldModalVisible] = useState(false)
  const [editingField, setEditingField] = useState<ProtocolField | null>(null)
  const [testData, setTestData] = useState('')
  const [parsedResult, setParsedResult] = useState<any>(null)

  const dataTypes = [
    { value: 'int8', label: 'int8 (1字节)', size: 1 },
    { value: 'uint8', label: 'uint8 (1字节)', size: 1 },
    { value: 'int16', label: 'int16 (2字节)', size: 2 },
    { value: 'uint16', label: 'uint16 (2字节)', size: 2 },
    { value: 'int32', label: 'int32 (4字节)', size: 4 },
    { value: 'uint32', label: 'uint32 (4字节)', size: 4 },
    { value: 'float', label: 'float (4字节)', size: 4 },
    { value: 'double', label: 'double (8字节)', size: 8 },
    { value: 'string', label: 'string (可变)', size: 1 },
  ]

  const columns = [
    {
      title: '字段名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '偏移量',
      dataIndex: 'offset',
      key: 'offset',
      render: (value: number) => `${value} 字节`,
    },
    {
      title: '长度',
      dataIndex: 'length',
      key: 'length',
      render: (value: number) => `${value} 字节`,
    },
    {
      title: '数据类型',
      dataIndex: 'type',
      key: 'type',
    },
    {
      title: '比例系数',
      dataIndex: 'scale',
      key: 'scale',
    },
    {
      title: '单位',
      dataIndex: 'unit',
      key: 'unit',
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: ProtocolField) => (
        <Space size="middle">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEditField(record)}
          >
            编辑
          </Button>
          <Button
            type="link"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDeleteField(record.id)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ]

  const handleAddField = () => {
    setEditingField(null)
    fieldForm.resetFields()
    setIsFieldModalVisible(true)
  }

  const handleEditField = (field: ProtocolField) => {
    setEditingField(field)
    fieldForm.setFieldsValue(field)
    setIsFieldModalVisible(true)
  }

  const handleDeleteField = (id: string) => {
    setProtocol(prev => ({
      ...prev,
      fields: prev.fields.filter(field => field.id !== id),
    }))
    message.success('字段删除成功')
  }

  const handleFieldSubmit = () => {
    fieldForm.validateFields().then(values => {
      const newField: ProtocolField = {
        id: editingField?.id || Date.now().toString(),
        ...values,
      }

      if (editingField) {
        setProtocol(prev => ({
          ...prev,
          fields: prev.fields.map(field =>
            field.id === editingField.id ? newField : field
          ),
        }))
        message.success('字段更新成功')
      } else {
        setProtocol(prev => ({
          ...prev,
          fields: [...prev.fields, newField],
        }))
        message.success('字段添加成功')
      }

      setIsFieldModalVisible(false)
    })
  }

  const handleSaveProtocol = () => {
    // 保存协议配置到后端
    message.success('协议配置保存成功')
  }

  const handleTestProtocol = () => {
    if (!testData.trim()) {
      message.error('请输入测试数据')
      return
    }

    // 模拟解析结果
    const mockResult = {
      frameHeader: protocol.frameHeader,
      fields: protocol.fields.reduce((acc, field) => {
        acc[field.name] = Math.random() * 100
        return acc
      }, {} as Record<string, number>),
      frameFooter: protocol.frameFooter,
    }

    setParsedResult(mockResult)
    message.success('协议解析完成')
  }

  return (
    <div style={{ padding: '0 24px' }}>
      <Card title="协议配置" style={{ marginBottom: 16 }}>
        <Tabs defaultActiveKey="basic">
          <TabPane tab="基础配置" key="basic">
            <Form
              form={form}
              layout="vertical"
              initialValues={protocol}
              onValuesChange={(_, values) => setProtocol({ ...protocol, ...values })}
            >
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label="协议名称"
                    name="name"
                    rules={[{ required: true, message: '请输入协议名称' }]}
                  >
                    <Input placeholder="请输入协议名称" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="数据包总长度"
                    name="totalLength"
                    rules={[{ required: true, message: '请输入数据包总长度' }]}
                  >
                    <InputNumber
                      min={1}
                      max={1024}
                      placeholder="字节数"
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label="帧头"
                    name="frameHeader"
                    rules={[{ required: true, message: '请输入帧头' }]}
                  >
                    <Input placeholder="例如: AA55" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="帧尾"
                    name="frameFooter"
                  >
                    <Input placeholder="例如: 55AA" />
                  </Form.Item>
                </Col>
              </Row>
            </Form>
          </TabPane>

          <TabPane tab="字段定义" key="fields">
            <div style={{ marginBottom: 16 }}>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleAddField}
              >
                添加字段
              </Button>
            </div>
            <Table
              columns={columns}
              dataSource={protocol.fields}
              rowKey="id"
              pagination={false}
            />
          </TabPane>

          <TabPane tab="协议测试" key="test">
            <Row gutter={16}>
              <Col span={12}>
                <Card title="测试数据输入" size="small">
                  <Input.TextArea
                    rows={6}
                    placeholder="请输入十六进制测试数据，例如: AA55010203040555AA"
                    value={testData}
                    onChange={(e) => setTestData(e.target.value)}
                  />
                  <div style={{ marginTop: 16 }}>
                    <Button type="primary" onClick={handleTestProtocol}>
                      解析测试
                    </Button>
                  </div>
                </Card>
              </Col>
              <Col span={12}>
                <Card title="解析结果" size="small">
                  {parsedResult ? (
                    <pre style={{ background: '#f5f5f5', padding: 16, borderRadius: 4 }}>
                      {JSON.stringify(parsedResult, null, 2)}
                    </pre>
                  ) : (
                    <div style={{ textAlign: 'center', color: '#999', padding: 40 }}>
                      暂无解析结果
                    </div>
                  )}
                </Card>
              </Col>
            </Row>
          </TabPane>
        </Tabs>

        <Divider />
        
        <Space>
          <Button type="primary" icon={<SaveOutlined />} onClick={handleSaveProtocol}>
            保存配置
          </Button>
          <Button icon={<ImportOutlined />}>
            导入配置
          </Button>
          <Button icon={<ExportOutlined />}>
            导出配置
          </Button>
        </Space>
      </Card>

      {/* 字段编辑模态框 */}
      <Modal
        title={editingField ? '编辑字段' : '添加字段'}
        open={isFieldModalVisible}
        onOk={handleFieldSubmit}
        onCancel={() => setIsFieldModalVisible(false)}
        width={600}
      >
        <Form form={fieldForm} layout="vertical">
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="字段名称"
                name="name"
                rules={[{ required: true, message: '请输入字段名称' }]}
              >
                <Input placeholder="例如: 温度" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="数据类型"
                name="type"
                rules={[{ required: true, message: '请选择数据类型' }]}
              >
                <Select placeholder="请选择数据类型">
                  {dataTypes.map(type => (
                    <Option key={type.value} value={type.value}>
                      {type.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                label="偏移量"
                name="offset"
                rules={[{ required: true, message: '请输入偏移量' }]}
              >
                <InputNumber min={0} placeholder="字节" style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="长度"
                name="length"
                rules={[{ required: true, message: '请输入长度' }]}
              >
                <InputNumber min={1} placeholder="字节" style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="比例系数"
                name="scale"
                initialValue={1}
              >
                <InputNumber step={0.1} placeholder="1.0" style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="单位" name="unit">
                <Input placeholder="例如: °C" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="描述" name="description">
                <Input placeholder="字段描述" />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </div>
  )
}

export default ProtocolConfig