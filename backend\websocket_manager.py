import json
import asyncio
from typing import Dict, List, Any
from fastapi import WebSocket
from loguru import logger


class WebSocketManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        self.active_connections: List[WebSocket] = []
        self.connection_data: Dict[WebSocket, Dict[str, Any]] = {}
    
    async def connect(self, websocket: WebSocket):
        """接受WebSocket连接"""
        await websocket.accept()
        self.active_connections.append(websocket)
        self.connection_data[websocket] = {
            "id": id(websocket),
            "subscriptions": set(),
            "user_id": None,
        }
        logger.info(f"WebSocket连接已建立: {id(websocket)}")
        
        # 发送连接成功消息
        await self.send_personal_message(websocket, {
            "type": "connection",
            "status": "connected",
            "connection_id": id(websocket)
        })
    
    def disconnect(self, websocket: WebSocket):
        """断开WebSocket连接"""
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
            connection_id = self.connection_data.get(websocket, {}).get("id")
            if websocket in self.connection_data:
                del self.connection_data[websocket]
            logger.info(f"WebSocket连接已断开: {connection_id}")
    
    async def send_personal_message(self, websocket: WebSocket, message: dict):
        """发送个人消息"""
        try:
            await websocket.send_text(json.dumps(message, ensure_ascii=False))
        except Exception as e:
            logger.error(f"发送个人消息失败: {e}")
            self.disconnect(websocket)
    
    async def broadcast(self, message: dict, subscription: str = None):
        """广播消息"""
        disconnected = []
        for websocket in self.active_connections:
            try:
                # 如果指定了订阅类型，只发送给订阅了该类型的连接
                if subscription:
                    subscriptions = self.connection_data.get(websocket, {}).get("subscriptions", set())
                    if subscription not in subscriptions:
                        continue
                
                await websocket.send_text(json.dumps(message, ensure_ascii=False))
            except Exception as e:
                logger.error(f"广播消息失败: {e}")
                disconnected.append(websocket)
        
        # 清理断开的连接
        for websocket in disconnected:
            self.disconnect(websocket)
    
    async def handle_message(self, websocket: WebSocket, data: dict):
        """处理接收到的消息"""
        try:
            message_type = data.get("type")
            
            if message_type == "subscribe":
                await self._handle_subscribe(websocket, data)
            elif message_type == "unsubscribe":
                await self._handle_unsubscribe(websocket, data)
            elif message_type == "ping":
                await self._handle_ping(websocket, data)
            elif message_type == "serial_send":
                await self._handle_serial_send(websocket, data)
            elif message_type == "flow_control":
                await self._handle_flow_control(websocket, data)
            else:
                logger.warning(f"未知消息类型: {message_type}")
                await self.send_personal_message(websocket, {
                    "type": "error",
                    "message": f"未知消息类型: {message_type}"
                })
        
        except Exception as e:
            logger.error(f"处理消息失败: {e}")
            await self.send_personal_message(websocket, {
                "type": "error",
                "message": str(e)
            })
    
    async def _handle_subscribe(self, websocket: WebSocket, data: dict):
        """处理订阅请求"""
        subscription = data.get("subscription")
        if subscription:
            self.connection_data[websocket]["subscriptions"].add(subscription)
            await self.send_personal_message(websocket, {
                "type": "subscribe_success",
                "subscription": subscription
            })
            logger.info(f"连接 {id(websocket)} 订阅了 {subscription}")
    
    async def _handle_unsubscribe(self, websocket: WebSocket, data: dict):
        """处理取消订阅请求"""
        subscription = data.get("subscription")
        if subscription:
            self.connection_data[websocket]["subscriptions"].discard(subscription)
            await self.send_personal_message(websocket, {
                "type": "unsubscribe_success",
                "subscription": subscription
            })
            logger.info(f"连接 {id(websocket)} 取消订阅了 {subscription}")
    
    async def _handle_ping(self, websocket: WebSocket, data: dict):
        """处理心跳请求"""
        await self.send_personal_message(websocket, {
            "type": "pong",
            "timestamp": data.get("timestamp")
        })
    
    async def _handle_serial_send(self, websocket: WebSocket, data: dict):
        """处理串口发送请求"""
        # 这里应该调用串口服务发送数据
        # 暂时模拟发送成功
        await self.send_personal_message(websocket, {
            "type": "serial_send_result",
            "success": True,
            "data": data.get("data"),
            "timestamp": asyncio.get_event_loop().time()
        })
    
    async def _handle_flow_control(self, websocket: WebSocket, data: dict):
        """处理流程控制请求"""
        action = data.get("action")
        flow_id = data.get("flow_id")
        
        # 这里应该调用流程引擎服务
        # 暂时模拟操作成功
        await self.send_personal_message(websocket, {
            "type": "flow_control_result",
            "action": action,
            "flow_id": flow_id,
            "success": True,
            "timestamp": asyncio.get_event_loop().time()
        })
    
    async def send_serial_data(self, data: dict):
        """发送串口数据到所有订阅的客户端"""
        await self.broadcast({
            "type": "serial_data",
            "data": data,
            "timestamp": asyncio.get_event_loop().time()
        }, subscription="serial_data")
    
    async def send_flow_status(self, flow_id: str, status: str, data: dict = None):
        """发送流程状态更新"""
        await self.broadcast({
            "type": "flow_status",
            "flow_id": flow_id,
            "status": status,
            "data": data or {},
            "timestamp": asyncio.get_event_loop().time()
        }, subscription="flow_status")
    
    async def send_system_notification(self, level: str, message: str, data: dict = None):
        """发送系统通知"""
        await self.broadcast({
            "type": "system_notification",
            "level": level,  # info, warning, error
            "message": message,
            "data": data or {},
            "timestamp": asyncio.get_event_loop().time()
        }, subscription="system_notifications")
    
    def get_connection_count(self) -> int:
        """获取当前连接数"""
        return len(self.active_connections)
    
    def get_connection_info(self) -> List[dict]:
        """获取连接信息"""
        return [
            {
                "id": data["id"],
                "subscriptions": list(data["subscriptions"]),
                "user_id": data["user_id"]
            }
            for data in self.connection_data.values()
        ]