import json
import struct
from typing import List, Optional, Dict, Any, <PERSON><PERSON>
from datetime import datetime
from loguru import logger
import re

from backend.models.base import Protocol, ProtocolField
from backend.utils.database import get_db_manager


class ProtocolService:
    """协议配置服务"""
    
    def __init__(self):
        self.db = get_db_manager()
    
    async def get_protocols(
        self, 
        page: int = 1, 
        page_size: int = 10,
        search: Optional[str] = None,
        category: Optional[str] = None,
        is_active: Optional[bool] = None
    ) -> Tuple[List[Protocol], int]:
        """获取协议列表"""
        try:
            return await self.db.get_protocols(
                page=page, page_size=page_size, search=search,
                category=category, is_active=is_active
            )
        except Exception as e:
            logger.error(f"获取协议列表失败: {e}")
            raise
    
    async def get_protocol(self, protocol_id: str) -> Optional[Protocol]:
        """获取协议详情"""
        try:
            return await self.db.get_protocol(protocol_id)
        except Exception as e:
            logger.error(f"获取协议详情失败: {e}")
            raise
    
    async def create_protocol(
        self,
        name: str,
        description: str = "",
        category: str = "custom",
        fields: List[ProtocolField] = None,
        config: Dict[str, Any] = None
    ) -> Protocol:
        """创建协议"""
        try:
            protocol_data = {
                "name": name,
                "description": description,
                "category": category,
                "fields": fields or [],
                "config": config or {},
                "is_active": True,
                "created_at": datetime.now(),
                "updated_at": datetime.now()
            }
            
            return await self.db.create_protocol(protocol_data)
        except Exception as e:
            logger.error(f"创建协议失败: {e}")
            raise
    
    async def update_protocol(
        self, 
        protocol_id: str, 
        updates: Dict[str, Any]
    ) -> Optional[Protocol]:
        """更新协议"""
        try:
            updates["updated_at"] = datetime.now()
            return await self.db.update_protocol(protocol_id, updates)
        except Exception as e:
            logger.error(f"更新协议失败: {e}")
            raise
    
    async def delete_protocol(self, protocol_id: str) -> bool:
        """删除协议"""
        try:
            return await self.db.delete_protocol(protocol_id)
        except Exception as e:
            logger.error(f"删除协议失败: {e}")
            raise
    
    async def test_protocol(
        self, 
        protocol_id: str, 
        test_data: str,
        data_format: str = "hex"
    ) -> Dict[str, Any]:
        """测试协议解析"""
        try:
            protocol = await self.get_protocol(protocol_id)
            if not protocol:
                raise Exception("协议不存在")
            
            # 转换测试数据
            if data_format == "hex":
                try:
                    data_bytes = bytes.fromhex(test_data.replace(" ", ""))
                except ValueError:
                    raise Exception("无效的十六进制数据")
            else:
                data_bytes = test_data.encode('utf-8')
            
            # 解析数据
            result = await self.parse_data(protocol, data_bytes)
            
            return {
                "success": True,
                "parsed_data": result,
                "original_data": test_data,
                "data_format": data_format,
                "data_length": len(data_bytes)
            }
            
        except Exception as e:
            logger.error(f"协议测试失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "original_data": test_data,
                "data_format": data_format
            }
    
    async def parse_data(
        self, 
        protocol: Protocol, 
        data: bytes
    ) -> Dict[str, Any]:
        """解析数据"""
        try:
            parsed_result = {
                "protocol_name": protocol.name,
                "protocol_id": protocol.id,
                "fields": {},
                "raw_data": data.hex().upper(),
                "data_length": len(data),
                "parse_time": datetime.now().isoformat()
            }
            
            offset = 0
            
            for field in protocol.fields:
                if offset >= len(data):
                    break
                
                try:
                    field_value, bytes_consumed = self._parse_field(field, data[offset:])
                    parsed_result["fields"][field.name] = {
                        "value": field_value,
                        "type": field.type,
                        "offset": offset,
                        "length": bytes_consumed,
                        "description": field.description
                    }
                    offset += bytes_consumed
                    
                except Exception as e:
                    logger.warning(f"解析字段 {field.name} 失败: {e}")
                    parsed_result["fields"][field.name] = {
                        "value": None,
                        "type": field.type,
                        "offset": offset,
                        "length": 0,
                        "error": str(e),
                        "description": field.description
                    }
            
            # 检查是否有未解析的数据
            if offset < len(data):
                parsed_result["remaining_data"] = data[offset:].hex().upper()
                parsed_result["remaining_length"] = len(data) - offset
            
            return parsed_result
            
        except Exception as e:
            logger.error(f"数据解析失败: {e}")
            raise
    
    def _parse_field(self, field: ProtocolField, data: bytes) -> Tuple[Any, int]:
        """解析单个字段"""
        if len(data) < field.length:
            raise Exception(f"数据长度不足，需要 {field.length} 字节，实际 {len(data)} 字节")
        
        field_data = data[:field.length]
        
        try:
            if field.type == "uint8":
                value = struct.unpack('>B', field_data)[0]
            elif field.type == "uint16":
                value = struct.unpack('>H', field_data)[0]
            elif field.type == "uint32":
                value = struct.unpack('>I', field_data)[0]
            elif field.type == "int8":
                value = struct.unpack('>b', field_data)[0]
            elif field.type == "int16":
                value = struct.unpack('>h', field_data)[0]
            elif field.type == "int32":
                value = struct.unpack('>i', field_data)[0]
            elif field.type == "float":
                value = struct.unpack('>f', field_data)[0]
            elif field.type == "double":
                value = struct.unpack('>d', field_data)[0]
            elif field.type == "string":
                value = field_data.decode('utf-8', errors='ignore').rstrip('\x00')
            elif field.type == "hex":
                value = field_data.hex().upper()
            elif field.type == "binary":
                value = ' '.join(f'{b:08b}' for b in field_data)
            else:
                value = field_data.hex().upper()
            
            # 应用转换公式
            if field.formula:
                try:
                    # 简单的数学表达式计算
                    formula = field.formula.replace('x', str(value))
                    value = eval(formula, {"__builtins__": {}})
                except Exception as e:
                    logger.warning(f"公式计算失败 {field.formula}: {e}")
            
            # 应用单位
            if field.unit:
                value = f"{value} {field.unit}"
            
            return value, field.length
            
        except Exception as e:
            raise Exception(f"解析字段类型 {field.type} 失败: {e}")
    
    async def validate_protocol(self, protocol_id: str) -> Dict[str, Any]:
        """验证协议配置"""
        try:
            protocol = await self.get_protocol(protocol_id)
            if not protocol:
                return {"valid": False, "errors": ["协议不存在"]}
            
            errors = []
            warnings = []
            
            # 检查基本信息
            if not protocol.name.strip():
                errors.append("协议名称不能为空")
            
            # 检查字段配置
            if not protocol.fields:
                warnings.append("协议没有定义字段")
            else:
                field_names = set()
                total_length = 0
                
                for i, field in enumerate(protocol.fields):
                    # 检查字段名重复
                    if field.name in field_names:
                        errors.append(f"字段名重复: {field.name}")
                    field_names.add(field.name)
                    
                    # 检查字段长度
                    if field.length <= 0:
                        errors.append(f"字段 {field.name} 长度必须大于0")
                    
                    # 检查字段类型与长度匹配
                    type_lengths = {
                        "uint8": 1, "int8": 1,
                        "uint16": 2, "int16": 2,
                        "uint32": 4, "int32": 4,
                        "float": 4, "double": 8
                    }
                    
                    if field.type in type_lengths:
                        expected_length = type_lengths[field.type]
                        if field.length != expected_length:
                            errors.append(
                                f"字段 {field.name} 类型 {field.type} "
                                f"长度应为 {expected_length}，实际为 {field.length}"
                            )
                    
                    # 检查公式语法
                    if field.formula:
                        try:
                            test_formula = field.formula.replace('x', '1')
                            eval(test_formula, {"__builtins__": {}})
                        except Exception:
                            errors.append(f"字段 {field.name} 公式语法错误: {field.formula}")
                    
                    total_length += field.length
                
                # 检查总长度
                if total_length > 1024:
                    warnings.append(f"协议总长度较大: {total_length} 字节")
            
            return {
                "valid": len(errors) == 0,
                "errors": errors,
                "warnings": warnings,
                "total_length": sum(f.length for f in protocol.fields),
                "field_count": len(protocol.fields)
            }
            
        except Exception as e:
            logger.error(f"协议验证失败: {e}")
            return {
                "valid": False,
                "errors": [f"验证过程出错: {str(e)}"]
            }
    
    async def duplicate_protocol(
        self, 
        protocol_id: str, 
        new_name: str
    ) -> Optional[Protocol]:
        """复制协议"""
        try:
            original = await self.get_protocol(protocol_id)
            if not original:
                return None
            
            # 创建副本
            protocol_data = {
                "name": new_name,
                "description": f"{original.description} (副本)",
                "category": original.category,
                "fields": original.fields,
                "config": original.config,
                "is_active": True,
                "created_at": datetime.now(),
                "updated_at": datetime.now()
            }
            
            return await self.db.create_protocol(protocol_data)
            
        except Exception as e:
            logger.error(f"复制协议失败: {e}")
            raise
    
    async def export_protocol(
        self, 
        protocol_id: str, 
        format: str = "json"
    ) -> Optional[Dict[str, Any]]:
        """导出协议"""
        try:
            protocol = await self.get_protocol(protocol_id)
            if not protocol:
                return None
            
            export_data = {
                "name": protocol.name,
                "description": protocol.description,
                "category": protocol.category,
                "fields": [field.dict() for field in protocol.fields],
                "config": protocol.config,
                "export_time": datetime.now().isoformat(),
                "version": "1.0"
            }
            
            if format == "json":
                return {
                    "content": json.dumps(export_data, indent=2, ensure_ascii=False),
                    "filename": f"{protocol.name}_protocol.json",
                    "content_type": "application/json"
                }
            else:
                raise Exception(f"不支持的导出格式: {format}")
            
        except Exception as e:
            logger.error(f"导出协议失败: {e}")
            raise
    
    async def import_protocol(
        self, 
        data: str, 
        format: str = "json"
    ) -> Protocol:
        """导入协议"""
        try:
            if format == "json":
                import_data = json.loads(data)
            else:
                raise Exception(f"不支持的导入格式: {format}")
            
            # 验证导入数据
            required_fields = ["name", "fields"]
            for field in required_fields:
                if field not in import_data:
                    raise Exception(f"缺少必需字段: {field}")
            
            # 转换字段数据
            fields = []
            for field_data in import_data["fields"]:
                field = ProtocolField(**field_data)
                fields.append(field)
            
            # 创建协议
            return await self.create_protocol(
                name=import_data["name"],
                description=import_data.get("description", ""),
                category=import_data.get("category", "imported"),
                fields=fields,
                config=import_data.get("config", {})
            )
            
        except Exception as e:
            logger.error(f"导入协议失败: {e}")
            raise
    
    async def get_field(self, protocol_id: str, field_id: str) -> Optional[ProtocolField]:
        """获取协议字段"""
        try:
            protocol = await self.get_protocol(protocol_id)
            if not protocol:
                return None
            
            for field in protocol.fields:
                if field.id == field_id:
                    return field
            
            return None
            
        except Exception as e:
            logger.error(f"获取协议字段失败: {e}")
            raise
    
    async def add_field(
        self, 
        protocol_id: str, 
        field: ProtocolField
    ) -> Optional[Protocol]:
        """添加协议字段"""
        try:
            protocol = await self.get_protocol(protocol_id)
            if not protocol:
                return None
            
            # 检查字段名是否重复
            for existing_field in protocol.fields:
                if existing_field.name == field.name:
                    raise Exception(f"字段名已存在: {field.name}")
            
            protocol.fields.append(field)
            
            return await self.update_protocol(protocol_id, {
                "fields": protocol.fields
            })
            
        except Exception as e:
            logger.error(f"添加协议字段失败: {e}")
            raise
    
    async def update_field(
        self, 
        protocol_id: str, 
        field_id: str, 
        updates: Dict[str, Any]
    ) -> Optional[Protocol]:
        """更新协议字段"""
        try:
            protocol = await self.get_protocol(protocol_id)
            if not protocol:
                return None
            
            for i, field in enumerate(protocol.fields):
                if field.id == field_id:
                    # 更新字段
                    field_dict = field.dict()
                    field_dict.update(updates)
                    protocol.fields[i] = ProtocolField(**field_dict)
                    
                    return await self.update_protocol(protocol_id, {
                        "fields": protocol.fields
                    })
            
            return None
            
        except Exception as e:
            logger.error(f"更新协议字段失败: {e}")
            raise
    
    async def delete_field(
        self, 
        protocol_id: str, 
        field_id: str
    ) -> Optional[Protocol]:
        """删除协议字段"""
        try:
            protocol = await self.get_protocol(protocol_id)
            if not protocol:
                return None
            
            original_count = len(protocol.fields)
            protocol.fields = [f for f in protocol.fields if f.id != field_id]
            
            if len(protocol.fields) == original_count:
                return None  # 字段不存在
            
            return await self.update_protocol(protocol_id, {
                "fields": protocol.fields
            })
            
        except Exception as e:
            logger.error(f"删除协议字段失败: {e}")
            raise
    
    async def get_categories(self) -> List[str]:
        """获取协议分类"""
        try:
            return await self.db.get_categories()
        except Exception as e:
            logger.error(f"获取协议分类失败: {e}")
            raise