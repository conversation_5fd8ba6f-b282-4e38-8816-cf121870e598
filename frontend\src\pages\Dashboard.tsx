import React, { useEffect } from 'react'
import { Card, Row, Col, Statistic, Progress, Timeline, Alert } from 'antd'
import {
  WifiOutlined,
  DatabaseOutlined,
  SettingOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
} from '@ant-design/icons'
import { useSerialStore } from '../stores/serialStore'
import EChartsReact from 'echarts-for-react'

const Dashboard: React.FC = () => {
  const { 
    isConnected, 
    receivedData, 
    lastData, 
    connectWebSocket,
    refreshPorts 
  } = useSerialStore()

  useEffect(() => {
    // 初始化WebSocket连接
    connectWebSocket()
    // 刷新串口列表
    refreshPorts()
  }, [])

  // 生成模拟图表数据
  const generateChartData = () => {
    const data = receivedData.slice(-20).map((item) => ({
      time: new Date(item.timestamp).toLocaleTimeString(),
      value: (item.parsed?.temperature as number) || Math.random() * 100,
    }))
    
    return {
      title: {
        text: '实时温度监控',
        left: 'center',
      },
      tooltip: {
        trigger: 'axis',
      },
      xAxis: {
        type: 'category',
        data: data.map(item => item.time),
      },
      yAxis: {
        type: 'value',
        name: '温度 (°C)',
      },
      series: [
        {
          name: '温度',
          type: 'line',
          smooth: true,
          data: data.map(item => item.value),
          lineStyle: {
            color: '#1890ff',
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(24, 144, 255, 0.3)' },
                { offset: 1, color: 'rgba(24, 144, 255, 0.1)' },
              ],
            },
          },
        },
      ],
    }
  }

  const timelineItems = [
    {
      color: 'green',
      children: (
        <div>
          <p>系统启动完成</p>
          <p style={{ color: '#999', fontSize: 12 }}>2024-06-24 10:30:00</p>
        </div>
      ),
    },
    {
      color: isConnected ? 'green' : 'red',
      children: (
        <div>
          <p>串口连接{isConnected ? '成功' : '断开'}</p>
          <p style={{ color: '#999', fontSize: 12 }}>
            {new Date().toLocaleString()}
          </p>
        </div>
      ),
    },
    {
      color: 'blue',
      children: (
        <div>
          <p>接收数据包: {receivedData.length} 条</p>
          <p style={{ color: '#999', fontSize: 12 }}>
            {lastData ? new Date(lastData.timestamp).toLocaleString() : '暂无数据'}
          </p>
        </div>
      ),
    },
  ]

  return (
    <div style={{ padding: '0 24px' }}>
      <Alert
        message="欢迎使用可视化上位机配置平台"
        description="您可以通过左侧菜单访问各个功能模块，开始配置您的串口通信协议和监控界面。"
        type="info"
        showIcon
        style={{ marginBottom: 24 }}
      />
      
      <Row gutter={[16, 16]}>
        {/* 状态统计卡片 */}
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="连接状态"
              value={isConnected ? '已连接' : '未连接'}
              prefix={<WifiOutlined />}
              valueStyle={{ color: isConnected ? '#3f8600' : '#cf1322' }}
            />
          </Card>
        </Col>
        
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="接收数据包"
              value={receivedData.length}
              prefix={<DatabaseOutlined />}
              suffix="条"
            />
          </Card>
        </Col>
        
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="当前温度"
              value={(lastData?.parsed?.temperature as number) || 0}
              prefix={<SettingOutlined />}
              suffix="°C"
              precision={1}
            />
          </Card>
        </Col>
        
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <div style={{ marginBottom: 8 }}>系统负载</div>
              <Progress
                type="circle"
                percent={Math.round(Math.random() * 100)}
                size={80}
                strokeColor={{
                  '0%': '#108ee9',
                  '100%': '#87d068',
                }}
              />
            </div>
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
        {/* 实时图表 */}
        <Col xs={24} lg={16}>
          <Card title="实时数据监控" extra={<a href="#">更多</a>}>
            <EChartsReact
              option={generateChartData()}
              style={{ height: 400 }}
              opts={{ renderer: 'canvas' }}
            />
          </Card>
        </Col>
        
        {/* 系统日志 */}
        <Col xs={24} lg={8}>
          <Card title="系统日志" extra={<a href="#">查看全部</a>}>
            <Timeline
              items={timelineItems}
              style={{ marginTop: 16 }}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
        {/* 快速操作 */}
        <Col xs={24}>
          <Card title="快速操作">
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={12} md={6}>
                <Card
                  hoverable
                  style={{ textAlign: 'center', cursor: 'pointer' }}
                  onClick={() => window.location.href = '/protocol'}
                >
                  <SettingOutlined style={{ fontSize: 32, color: '#1890ff' }} />
                  <div style={{ marginTop: 8 }}>协议配置</div>
                </Card>
              </Col>
              
              <Col xs={24} sm={12} md={6}>
                <Card
                  hoverable
                  style={{ textAlign: 'center', cursor: 'pointer' }}
                  onClick={() => window.location.href = '/interface'}
                >
                  <SettingOutlined style={{ fontSize: 32, color: '#52c41a' }} />
                  <div style={{ marginTop: 8 }}>界面设计</div>
                </Card>
              </Col>
              
              <Col xs={24} sm={12} md={6}>
                <Card
                  hoverable
                  style={{ textAlign: 'center', cursor: 'pointer' }}
                  onClick={() => window.location.href = '/flow'}
                >
                  <CheckCircleOutlined style={{ fontSize: 32, color: '#fa8c16' }} />
                  <div style={{ marginTop: 8 }}>流程引擎</div>
                </Card>
              </Col>
              
              <Col xs={24} sm={12} md={6}>
                <Card
                  hoverable
                  style={{ textAlign: 'center', cursor: 'pointer' }}
                  onClick={() => window.location.href = '/monitor'}
                >
                  <ClockCircleOutlined style={{ fontSize: 32, color: '#eb2f96' }} />
                  <div style={{ marginTop: 8 }}>串口监控</div>
                </Card>
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>
    </div>
  )
}

export default Dashboard