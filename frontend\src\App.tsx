import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import { Layout } from 'antd'
import MainLayout from './components/Layout/MainLayout'
import Dashboard from './pages/Dashboard'
import ProtocolConfig from './pages/ProtocolConfig'
import InterfaceDesign from './pages/InterfaceDesign'
import FlowEngine from './pages/FlowEngine'
import SerialMonitor from './pages/SerialMonitor'
import ProjectManagement from './pages/ProjectManagement'

const { Content } = Layout

function App() {
  return (
    <Router>
      <MainLayout>
        <Content style={{ padding: '24px', minHeight: 'calc(100vh - 64px)' }}>
          <Routes>
            <Route path="/" element={<Dashboard />} />
            <Route path="/protocol" element={<ProtocolConfig />} />
            <Route path="/interface" element={<InterfaceDesign />} />
            <Route path="/flow" element={<FlowEngine />} />
            <Route path="/monitor" element={<SerialMonitor />} />
            <Route path="/project" element={<ProjectManagement />} />
          </Routes>
        </Content>
      </MainLayout>
    </Router>
  )
}

export default App