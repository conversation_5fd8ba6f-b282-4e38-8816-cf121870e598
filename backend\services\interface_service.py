import json
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime
from loguru import logger

from backend.models.base import InterfaceDesign, ComponentConfig
from backend.utils.database import get_db_manager


class InterfaceService:
    """界面设计服务"""
    
    def __init__(self):
        self.db = get_db_manager()
        self.component_templates = self._load_component_templates()
    
    def _load_component_templates(self) -> Dict[str, Dict[str, Any]]:
        """加载组件模板"""
        return {
            "text": {
                "type": "text",
                "name": "文本显示",
                "icon": "FileTextOutlined",
                "defaultProps": {
                    "title": "文本标题",
                    "fontSize": 14,
                    "color": "#000000",
                    "backgroundColor": "transparent",
                    "textAlign": "left",
                    "fontWeight": "normal"
                },
                "configSchema": {
                    "title": {"type": "string", "label": "标题"},
                    "fontSize": {"type": "number", "label": "字体大小", "min": 8, "max": 72},
                    "color": {"type": "color", "label": "文字颜色"},
                    "backgroundColor": {"type": "color", "label": "背景颜色"},
                    "textAlign": {"type": "select", "label": "对齐方式", "options": ["left", "center", "right"]},
                    "fontWeight": {"type": "select", "label": "字体粗细", "options": ["normal", "bold"]}
                }
            },
            "number": {
                "type": "number",
                "name": "数值显示",
                "icon": "NumberOutlined",
                "defaultProps": {
                    "title": "数值标题",
                    "value": 0,
                    "unit": "",
                    "precision": 2,
                    "fontSize": 24,
                    "color": "#1890ff",
                    "backgroundColor": "transparent"
                },
                "configSchema": {
                    "title": {"type": "string", "label": "标题"},
                    "unit": {"type": "string", "label": "单位"},
                    "precision": {"type": "number", "label": "小数位数", "min": 0, "max": 6},
                    "fontSize": {"type": "number", "label": "字体大小", "min": 8, "max": 72},
                    "color": {"type": "color", "label": "数值颜色"},
                    "backgroundColor": {"type": "color", "label": "背景颜色"}
                }
            },
            "chart": {
                "type": "chart",
                "name": "图表",
                "icon": "LineChartOutlined",
                "defaultProps": {
                    "title": "图表标题",
                    "chartType": "line",
                    "xAxisLabel": "X轴",
                    "yAxisLabel": "Y轴",
                    "showGrid": True,
                    "showLegend": True,
                    "backgroundColor": "transparent"
                },
                "configSchema": {
                    "title": {"type": "string", "label": "标题"},
                    "chartType": {"type": "select", "label": "图表类型", "options": ["line", "bar", "area", "pie"]},
                    "xAxisLabel": {"type": "string", "label": "X轴标签"},
                    "yAxisLabel": {"type": "string", "label": "Y轴标签"},
                    "showGrid": {"type": "boolean", "label": "显示网格"},
                    "showLegend": {"type": "boolean", "label": "显示图例"},
                    "backgroundColor": {"type": "color", "label": "背景颜色"}
                }
            },
            "gauge": {
                "type": "gauge",
                "name": "仪表盘",
                "icon": "DashboardOutlined",
                "defaultProps": {
                    "title": "仪表盘标题",
                    "value": 0,
                    "min": 0,
                    "max": 100,
                    "unit": "%",
                    "color": "#1890ff",
                    "backgroundColor": "transparent"
                },
                "configSchema": {
                    "title": {"type": "string", "label": "标题"},
                    "min": {"type": "number", "label": "最小值"},
                    "max": {"type": "number", "label": "最大值"},
                    "unit": {"type": "string", "label": "单位"},
                    "color": {"type": "color", "label": "指针颜色"},
                    "backgroundColor": {"type": "color", "label": "背景颜色"}
                }
            },
            "button": {
                "type": "button",
                "name": "按钮",
                "icon": "PlayCircleOutlined",
                "defaultProps": {
                    "text": "按钮",
                    "type": "primary",
                    "size": "middle",
                    "disabled": False,
                    "backgroundColor": "#1890ff",
                    "textColor": "#ffffff"
                },
                "configSchema": {
                    "text": {"type": "string", "label": "按钮文字"},
                    "type": {"type": "select", "label": "按钮类型", "options": ["primary", "default", "dashed", "text", "link"]},
                    "size": {"type": "select", "label": "按钮大小", "options": ["small", "middle", "large"]},
                    "disabled": {"type": "boolean", "label": "禁用状态"},
                    "backgroundColor": {"type": "color", "label": "背景颜色"},
                    "textColor": {"type": "color", "label": "文字颜色"}
                }
            },
            "switch": {
                "type": "switch",
                "name": "开关",
                "icon": "SwitchOutlined",
                "defaultProps": {
                    "title": "开关标题",
                    "checked": False,
                    "disabled": False,
                    "size": "default",
                    "checkedChildren": "开",
                    "unCheckedChildren": "关"
                },
                "configSchema": {
                    "title": {"type": "string", "label": "标题"},
                    "disabled": {"type": "boolean", "label": "禁用状态"},
                    "size": {"type": "select", "label": "开关大小", "options": ["small", "default"]},
                    "checkedChildren": {"type": "string", "label": "选中时文字"},
                    "unCheckedChildren": {"type": "string", "label": "未选中时文字"}
                }
            }
        }
    
    async def get_interfaces(
        self, 
        page: int = 1, 
        page_size: int = 10,
        search: Optional[str] = None,
        project_id: Optional[str] = None,
        is_active: Optional[bool] = None
    ) -> Tuple[List[InterfaceDesign], int]:
        """获取界面列表"""
        try:
            return await self.db.get_interfaces(
                page=page, page_size=page_size, search=search,
                project_id=project_id, is_active=is_active
            )
        except Exception as e:
            logger.error(f"获取界面列表失败: {e}")
            raise
    
    async def get_interface(self, interface_id: str) -> Optional[InterfaceDesign]:
        """获取界面详情"""
        try:
            return await self.db.get_interface(interface_id)
        except Exception as e:
            logger.error(f"获取界面详情失败: {e}")
            raise
    
    async def create_interface(
        self,
        name: str,
        description: str = "",
        project_id: Optional[str] = None,
        components: List[ComponentConfig] = None,
        layout: Dict[str, Any] = None,
        config: Dict[str, Any] = None
    ) -> InterfaceDesign:
        """创建界面"""
        try:
            interface_data = {
                "name": name,
                "description": description,
                "project_id": project_id,
                "components": components or [],
                "layout": layout or {"cols": 12, "rowHeight": 60, "margin": [10, 10]},
                "config": config or {},
                "is_active": True,
                "created_at": datetime.now(),
                "updated_at": datetime.now()
            }
            
            return await self.db.create_interface(interface_data)
        except Exception as e:
            logger.error(f"创建界面失败: {e}")
            raise
    
    async def update_interface(
        self, 
        interface_id: str, 
        updates: Dict[str, Any]
    ) -> Optional[InterfaceDesign]:
        """更新界面"""
        try:
            updates["updated_at"] = datetime.now()
            return await self.db.update_interface(interface_id, updates)
        except Exception as e:
            logger.error(f"更新界面失败: {e}")
            raise
    
    async def delete_interface(self, interface_id: str) -> bool:
        """删除界面"""
        try:
            return await self.db.delete_interface(interface_id)
        except Exception as e:
            logger.error(f"删除界面失败: {e}")
            raise
    
    async def get_component(
        self, 
        interface_id: str, 
        component_id: str
    ) -> Optional[ComponentConfig]:
        """获取组件"""
        try:
            interface = await self.get_interface(interface_id)
            if not interface:
                return None
            
            for component in interface.components:
                if component.id == component_id:
                    return component
            
            return None
            
        except Exception as e:
            logger.error(f"获取组件失败: {e}")
            raise
    
    async def add_component(
        self, 
        interface_id: str, 
        component: ComponentConfig
    ) -> Optional[InterfaceDesign]:
        """添加组件"""
        try:
            interface = await self.get_interface(interface_id)
            if not interface:
                return None
            
            # 检查组件ID是否重复
            for existing_component in interface.components:
                if existing_component.id == component.id:
                    raise Exception(f"组件ID已存在: {component.id}")
            
            interface.components.append(component)
            
            return await self.update_interface(interface_id, {
                "components": interface.components
            })
            
        except Exception as e:
            logger.error(f"添加组件失败: {e}")
            raise
    
    async def update_component(
        self, 
        interface_id: str, 
        component_id: str, 
        updates: Dict[str, Any]
    ) -> Optional[InterfaceDesign]:
        """更新组件"""
        try:
            interface = await self.get_interface(interface_id)
            if not interface:
                return None
            
            for i, component in enumerate(interface.components):
                if component.id == component_id:
                    # 更新组件
                    component_dict = component.dict()
                    component_dict.update(updates)
                    interface.components[i] = ComponentConfig(**component_dict)
                    
                    return await self.update_interface(interface_id, {
                        "components": interface.components
                    })
            
            return None
            
        except Exception as e:
            logger.error(f"更新组件失败: {e}")
            raise
    
    async def delete_component(
        self, 
        interface_id: str, 
        component_id: str
    ) -> Optional[InterfaceDesign]:
        """删除组件"""
        try:
            interface = await self.get_interface(interface_id)
            if not interface:
                return None
            
            original_count = len(interface.components)
            interface.components = [c for c in interface.components if c.id != component_id]
            
            if len(interface.components) == original_count:
                return None  # 组件不存在
            
            return await self.update_interface(interface_id, {
                "components": interface.components
            })
            
        except Exception as e:
            logger.error(f"删除组件失败: {e}")
            raise
    
    async def duplicate_interface(
        self, 
        interface_id: str, 
        new_name: str
    ) -> Optional[InterfaceDesign]:
        """复制界面"""
        try:
            original = await self.get_interface(interface_id)
            if not original:
                return None
            
            # 创建副本
            interface_data = {
                "name": new_name,
                "description": f"{original.description} (副本)",
                "project_id": original.project_id,
                "components": original.components,
                "layout": original.layout,
                "config": original.config,
                "is_active": True,
                "created_at": datetime.now(),
                "updated_at": datetime.now()
            }
            
            return await self.db.create_interface(interface_data)
            
        except Exception as e:
            logger.error(f"复制界面失败: {e}")
            raise
    
    async def preview_interface(
        self, 
        interface_id: str
    ) -> Optional[Dict[str, Any]]:
        """预览界面"""
        try:
            interface = await self.get_interface(interface_id)
            if not interface:
                return None
            
            # 生成预览数据
            preview_data = {
                "interface": interface.dict(),
                "component_templates": self.component_templates,
                "preview_time": datetime.now().isoformat()
            }
            
            return preview_data
            
        except Exception as e:
            logger.error(f"预览界面失败: {e}")
            raise
    
    async def validate_interface(
        self, 
        interface_id: str
    ) -> Dict[str, Any]:
        """验证界面配置"""
        try:
            interface = await self.get_interface(interface_id)
            if not interface:
                return {"valid": False, "errors": ["界面不存在"]}
            
            errors = []
            warnings = []
            
            # 检查基本信息
            if not interface.name.strip():
                errors.append("界面名称不能为空")
            
            # 检查组件配置
            if not interface.components:
                warnings.append("界面没有添加组件")
            else:
                component_ids = set()
                
                for i, component in enumerate(interface.components):
                    # 检查组件ID重复
                    if component.id in component_ids:
                        errors.append(f"组件ID重复: {component.id}")
                    component_ids.add(component.id)
                    
                    # 检查组件类型
                    if component.type not in self.component_templates:
                        errors.append(f"未知的组件类型: {component.type}")
                    
                    # 检查布局配置
                    if not component.layout:
                        errors.append(f"组件 {component.id} 缺少布局配置")
                    else:
                        required_layout_fields = ["x", "y", "w", "h"]
                        for field in required_layout_fields:
                            if field not in component.layout:
                                errors.append(f"组件 {component.id} 布局缺少 {field} 配置")
                    
                    # 检查数据绑定
                    if component.data_binding:
                        if not component.data_binding.get("source"):
                            warnings.append(f"组件 {component.id} 数据绑定缺少数据源")
            
            # 检查布局冲突
            layout_conflicts = self._check_layout_conflicts(interface.components)
            if layout_conflicts:
                warnings.extend(layout_conflicts)
            
            return {
                "valid": len(errors) == 0,
                "errors": errors,
                "warnings": warnings,
                "component_count": len(interface.components)
            }
            
        except Exception as e:
            logger.error(f"界面验证失败: {e}")
            return {
                "valid": False,
                "errors": [f"验证过程出错: {str(e)}"]
            }
    
    def _check_layout_conflicts(self, components: List[ComponentConfig]) -> List[str]:
        """检查布局冲突"""
        conflicts = []
        
        for i, comp1 in enumerate(components):
            for j, comp2 in enumerate(components[i+1:], i+1):
                if self._components_overlap(comp1, comp2):
                    conflicts.append(
                        f"组件 {comp1.id} 和 {comp2.id} 布局重叠"
                    )
        
        return conflicts
    
    def _components_overlap(self, comp1: ComponentConfig, comp2: ComponentConfig) -> bool:
        """检查两个组件是否重叠"""
        if not comp1.layout or not comp2.layout:
            return False
        
        x1, y1, w1, h1 = comp1.layout["x"], comp1.layout["y"], comp1.layout["w"], comp1.layout["h"]
        x2, y2, w2, h2 = comp2.layout["x"], comp2.layout["y"], comp2.layout["w"], comp2.layout["h"]
        
        return not (x1 + w1 <= x2 or x2 + w2 <= x1 or y1 + h1 <= y2 or y2 + h2 <= y1)
    
    async def export_interface(
        self, 
        interface_id: str, 
        format: str = "json"
    ) -> Optional[Dict[str, Any]]:
        """导出界面"""
        try:
            interface = await self.get_interface(interface_id)
            if not interface:
                return None
            
            export_data = {
                "name": interface.name,
                "description": interface.description,
                "components": [comp.dict() for comp in interface.components],
                "layout": interface.layout,
                "config": interface.config,
                "export_time": datetime.now().isoformat(),
                "version": "1.0"
            }
            
            if format == "json":
                return {
                    "content": json.dumps(export_data, indent=2, ensure_ascii=False),
                    "filename": f"{interface.name}_interface.json",
                    "content_type": "application/json"
                }
            else:
                raise Exception(f"不支持的导出格式: {format}")
            
        except Exception as e:
            logger.error(f"导出界面失败: {e}")
            raise
    
    async def import_interface(
        self, 
        data: str, 
        format: str = "json",
        project_id: Optional[str] = None
    ) -> InterfaceDesign:
        """导入界面"""
        try:
            if format == "json":
                import_data = json.loads(data)
            else:
                raise Exception(f"不支持的导入格式: {format}")
            
            # 验证导入数据
            required_fields = ["name", "components"]
            for field in required_fields:
                if field not in import_data:
                    raise Exception(f"缺少必需字段: {field}")
            
            # 转换组件数据
            components = []
            for comp_data in import_data["components"]:
                component = ComponentConfig(**comp_data)
                components.append(component)
            
            # 创建界面
            return await self.create_interface(
                name=import_data["name"],
                description=import_data.get("description", ""),
                project_id=project_id,
                components=components,
                layout=import_data.get("layout", {}),
                config=import_data.get("config", {})
            )
            
        except Exception as e:
            logger.error(f"导入界面失败: {e}")
            raise
    
    async def update_layout(
        self, 
        interface_id: str, 
        layout_updates: List[Dict[str, Any]]
    ) -> Optional[InterfaceDesign]:
        """更新布局"""
        try:
            interface = await self.get_interface(interface_id)
            if not interface:
                return None
            
            # 更新组件布局
            for update in layout_updates:
                component_id = update.get("id")
                new_layout = update.get("layout")
                
                if not component_id or not new_layout:
                    continue
                
                for component in interface.components:
                    if component.id == component_id:
                        component.layout.update(new_layout)
                        break
            
            return await self.update_interface(interface_id, {
                "components": interface.components
            })
            
        except Exception as e:
            logger.error(f"更新布局失败: {e}")
            raise
    
    async def bind_data(
        self, 
        interface_id: str, 
        component_id: str, 
        data_binding: Dict[str, Any]
    ) -> Optional[InterfaceDesign]:
        """绑定数据"""
        try:
            return await self.update_component(interface_id, component_id, {
                "data_binding": data_binding
            })
            
        except Exception as e:
            logger.error(f"绑定数据失败: {e}")
            raise
    
    async def get_component_templates(self) -> Dict[str, Dict[str, Any]]:
        """获取组件模板"""
        return self.component_templates