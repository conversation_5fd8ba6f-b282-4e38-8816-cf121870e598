from typing import List, Optional
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from loguru import logger

from backend.models.base import (
    ResponseModel, SerialPortConfig, SerialPortStatus, 
    SerialData, SerialDataFormat
)
from backend.services.serial_service import SerialService

router = APIRouter()

# 获取串口服务实例
def get_serial_service() -> SerialService:
    return SerialService()


class SerialSendRequest(BaseModel):
    """串口发送请求模型"""
    data: str
    format: SerialDataFormat = SerialDataFormat.ASCII


class SerialConnectRequest(BaseModel):
    """串口连接请求模型"""
    config: SerialPortConfig


@router.get("/ports", response_model=ResponseModel)
async def get_available_ports(serial_service: SerialService = Depends(get_serial_service)):
    """获取可用串口列表"""
    try:
        ports = await serial_service.get_available_ports()
        return ResponseModel(data=ports, message="获取串口列表成功")
    except Exception as e:
        logger.error(f"获取串口列表失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/status", response_model=ResponseModel)
async def get_serial_status(serial_service: SerialService = Depends(get_serial_service)):
    """获取串口状态"""
    try:
        status = await serial_service.get_status()
        return ResponseModel(data=status, message="获取串口状态成功")
    except Exception as e:
        logger.error(f"获取串口状态失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/connect", response_model=ResponseModel)
async def connect_serial(
    request: SerialConnectRequest,
    serial_service: SerialService = Depends(get_serial_service)
):
    """连接串口"""
    try:
        success = await serial_service.connect(request.config)
        if success:
            return ResponseModel(message="串口连接成功")
        else:
            raise HTTPException(status_code=400, detail="串口连接失败")
    except Exception as e:
        logger.error(f"串口连接失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/disconnect", response_model=ResponseModel)
async def disconnect_serial(serial_service: SerialService = Depends(get_serial_service)):
    """断开串口连接"""
    try:
        success = await serial_service.disconnect()
        if success:
            return ResponseModel(message="串口断开成功")
        else:
            raise HTTPException(status_code=400, detail="串口断开失败")
    except Exception as e:
        logger.error(f"串口断开失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/send", response_model=ResponseModel)
async def send_data(
    request: SerialSendRequest,
    serial_service: SerialService = Depends(get_serial_service)
):
    """发送串口数据"""
    try:
        success = await serial_service.send_data(request.data, request.format)
        if success:
            return ResponseModel(message="数据发送成功")
        else:
            raise HTTPException(status_code=400, detail="数据发送失败")
    except Exception as e:
        logger.error(f"数据发送失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/history", response_model=ResponseModel)
async def get_data_history(
    limit: int = 100,
    direction: Optional[str] = None,
    serial_service: SerialService = Depends(get_serial_service)
):
    """获取串口数据历史"""
    try:
        history = await serial_service.get_data_history(limit=limit, direction=direction)
        return ResponseModel(data=history, message="获取数据历史成功")
    except Exception as e:
        logger.error(f"获取数据历史失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/history", response_model=ResponseModel)
async def clear_data_history(serial_service: SerialService = Depends(get_serial_service)):
    """清空串口数据历史"""
    try:
        success = await serial_service.clear_data_history()
        if success:
            return ResponseModel(message="数据历史清空成功")
        else:
            raise HTTPException(status_code=400, detail="数据历史清空失败")
    except Exception as e:
        logger.error(f"数据历史清空失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/statistics", response_model=ResponseModel)
async def get_statistics(serial_service: SerialService = Depends(get_serial_service)):
    """获取串口统计信息"""
    try:
        stats = await serial_service.get_statistics()
        return ResponseModel(data=stats, message="获取统计信息成功")
    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/test", response_model=ResponseModel)
async def test_connection(
    config: SerialPortConfig,
    serial_service: SerialService = Depends(get_serial_service)
):
    """测试串口连接"""
    try:
        result = await serial_service.test_connection(config)
        return ResponseModel(data=result, message="串口测试完成")
    except Exception as e:
        logger.error(f"串口测试失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/config", response_model=ResponseModel)
async def get_current_config(serial_service: SerialService = Depends(get_serial_service)):
    """获取当前串口配置"""
    try:
        config = await serial_service.get_current_config()
        return ResponseModel(data=config, message="获取配置成功")
    except Exception as e:
        logger.error(f"获取配置失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/config", response_model=ResponseModel)
async def update_config(
    config: SerialPortConfig,
    serial_service: SerialService = Depends(get_serial_service)
):
    """更新串口配置"""
    try:
        success = await serial_service.update_config(config)
        if success:
            return ResponseModel(message="配置更新成功")
        else:
            raise HTTPException(status_code=400, detail="配置更新失败")
    except Exception as e:
        logger.error(f"配置更新失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))