import os
import re
import hashlib
import secrets
import string
from typing import List, Optional
from pathlib import Path


def generate_token(length: int = 32) -> str:
    """生成安全的随机令牌
    
    Args:
        length: 令牌长度
        
    Returns:
        随机令牌字符串
    """
    return secrets.token_hex(length)


def generate_password(length: int = 12, include_symbols: bool = True) -> str:
    """生成安全的随机密码
    
    Args:
        length: 密码长度
        include_symbols: 是否包含特殊符号
        
    Returns:
        随机密码
    """
    characters = string.ascii_letters + string.digits
    if include_symbols:
        characters += "!@#$%^&*()_+-=[]{}|;:,.<>?"
    
    return ''.join(secrets.choice(characters) for _ in range(length))


def hash_password(password: str, salt: Optional[str] = None) -> tuple[str, str]:
    """哈希密码
    
    Args:
        password: 原始密码
        salt: 盐值（如果为None则自动生成）
        
    Returns:
        (哈希值, 盐值) 元组
    """
    if salt is None:
        salt = secrets.token_hex(16)
    
    # 使用PBKDF2进行哈希
    hash_value = hashlib.pbkdf2_hmac(
        'sha256',
        password.encode('utf-8'),
        salt.encode('utf-8'),
        100000  # 迭代次数
    )
    
    return hash_value.hex(), salt


def verify_password(password: str, hash_value: str, salt: str) -> bool:
    """验证密码
    
    Args:
        password: 输入的密码
        hash_value: 存储的哈希值
        salt: 盐值
        
    Returns:
        密码是否正确
    """
    computed_hash, _ = hash_password(password, salt)
    return secrets.compare_digest(computed_hash, hash_value)


def sanitize_filename(filename: str, replacement: str = "_") -> str:
    """清理文件名，移除不安全字符
    
    Args:
        filename: 原始文件名
        replacement: 替换字符
        
    Returns:
        清理后的文件名
    """
    # 移除或替换不安全的字符
    unsafe_chars = r'[<>:"/\\|?*]'
    sanitized = re.sub(unsafe_chars, replacement, filename)
    
    # 移除控制字符
    sanitized = ''.join(char for char in sanitized if ord(char) >= 32)
    
    # 移除开头和结尾的点和空格
    sanitized = sanitized.strip('. ')
    
    # 确保文件名不为空
    if not sanitized:
        sanitized = "unnamed_file"
    
    # 限制长度
    if len(sanitized) > 255:
        name, ext = os.path.splitext(sanitized)
        max_name_length = 255 - len(ext)
        sanitized = name[:max_name_length] + ext
    
    return sanitized


def validate_file_type(filename: str, allowed_extensions: List[str]) -> bool:
    """验证文件类型
    
    Args:
        filename: 文件名
        allowed_extensions: 允许的扩展名列表
        
    Returns:
        是否为允许的文件类型
    """
    if not filename or not allowed_extensions:
        return False
    
    file_ext = Path(filename).suffix.lower().lstrip('.')
    allowed_exts = [ext.lower().lstrip('.') for ext in allowed_extensions]
    
    return file_ext in allowed_exts


def validate_file_size(file_path: str, max_size_mb: float) -> bool:
    """验证文件大小
    
    Args:
        file_path: 文件路径
        max_size_mb: 最大文件大小（MB）
        
    Returns:
        文件大小是否符合要求
    """
    try:
        file_size = os.path.getsize(file_path)
        max_size_bytes = max_size_mb * 1024 * 1024
        return file_size <= max_size_bytes
    except OSError:
        return False


def calculate_file_hash(file_path: str, algorithm: str = 'sha256') -> str:
    """计算文件哈希值
    
    Args:
        file_path: 文件路径
        algorithm: 哈希算法（md5, sha1, sha256, sha512）
        
    Returns:
        文件哈希值
    """
    try:
        hash_obj = hashlib.new(algorithm)
        
        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_obj.update(chunk)
        
        return hash_obj.hexdigest()
    except (OSError, ValueError):
        return ""


def secure_delete_file(file_path: str, passes: int = 3) -> bool:
    """安全删除文件（多次覆写）
    
    Args:
        file_path: 文件路径
        passes: 覆写次数
        
    Returns:
        是否删除成功
    """
    try:
        if not os.path.exists(file_path):
            return True
        
        file_size = os.path.getsize(file_path)
        
        # 多次覆写文件内容
        with open(file_path, 'r+b') as f:
            for _ in range(passes):
                f.seek(0)
                f.write(os.urandom(file_size))
                f.flush()
                os.fsync(f.fileno())
        
        # 删除文件
        os.remove(file_path)
        return True
    except OSError:
        return False


def validate_path_traversal(file_path: str, base_directory: str) -> bool:
    """验证路径是否存在目录遍历攻击
    
    Args:
        file_path: 文件路径
        base_directory: 基础目录
        
    Returns:
        路径是否安全
    """
    try:
        # 规范化路径
        normalized_path = os.path.normpath(file_path)
        normalized_base = os.path.normpath(base_directory)
        
        # 转换为绝对路径
        abs_path = os.path.abspath(os.path.join(normalized_base, normalized_path))
        abs_base = os.path.abspath(normalized_base)
        
        # 检查是否在基础目录内
        return abs_path.startswith(abs_base)
    except (OSError, ValueError):
        return False


def escape_html(text: str) -> str:
    """转义HTML特殊字符
    
    Args:
        text: 原始文本
        
    Returns:
        转义后的文本
    """
    html_escape_table = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#x27;',
        '/': '&#x2F;'
    }
    
    return ''.join(html_escape_table.get(char, char) for char in text)


def escape_sql(text: str) -> str:
    """转义SQL特殊字符
    
    Args:
        text: 原始文本
        
    Returns:
        转义后的文本
    """
    # 简单的SQL转义，实际应用中建议使用参数化查询
    return text.replace("'", "''").replace('"', '""')


def validate_input_length(text: str, max_length: int) -> bool:
    """验证输入长度
    
    Args:
        text: 输入文本
        max_length: 最大长度
        
    Returns:
        长度是否符合要求
    """
    return len(text) <= max_length


def validate_input_pattern(text: str, pattern: str) -> bool:
    """验证输入模式
    
    Args:
        text: 输入文本
        pattern: 正则表达式模式
        
    Returns:
        是否匹配模式
    """
    try:
        return bool(re.match(pattern, text))
    except re.error:
        return False


def rate_limit_check(identifier: str, max_requests: int, time_window: int) -> bool:
    """简单的速率限制检查
    
    Args:
        identifier: 标识符（如IP地址）
        max_requests: 最大请求数
        time_window: 时间窗口（秒）
        
    Returns:
        是否允许请求
    """
    # 这里应该使用Redis或其他存储来实现
    # 这只是一个示例实现
    import time
    
    current_time = time.time()
    # 实际实现中需要持久化存储请求记录
    # 这里只是返回True作为示例
    return True


def generate_csrf_token() -> str:
    """生成CSRF令牌
    
    Returns:
        CSRF令牌
    """
    return secrets.token_urlsafe(32)


def validate_csrf_token(token: str, expected_token: str) -> bool:
    """验证CSRF令牌
    
    Args:
        token: 提交的令牌
        expected_token: 期望的令牌
        
    Returns:
        令牌是否有效
    """
    return secrets.compare_digest(token, expected_token)


def mask_sensitive_data(data: str, mask_char: str = '*', visible_chars: int = 4) -> str:
    """掩码敏感数据
    
    Args:
        data: 敏感数据
        mask_char: 掩码字符
        visible_chars: 可见字符数
        
    Returns:
        掩码后的数据
    """
    if len(data) <= visible_chars:
        return mask_char * len(data)
    
    visible_part = data[:visible_chars]
    masked_part = mask_char * (len(data) - visible_chars)
    
    return visible_part + masked_part


def validate_api_key(api_key: str, valid_keys: List[str]) -> bool:
    """验证API密钥
    
    Args:
        api_key: API密钥
        valid_keys: 有效密钥列表
        
    Returns:
        密钥是否有效
    """
    return any(secrets.compare_digest(api_key, valid_key) for valid_key in valid_keys)


class SecurityConfig:
    """安全配置类"""
    
    def __init__(self):
        self.max_file_size_mb = 10
        self.allowed_file_extensions = ['.txt', '.json', '.csv', '.xml']
        self.max_input_length = 1000
        self.password_min_length = 8
        self.token_expiry_hours = 24
        self.rate_limit_requests = 100
        self.rate_limit_window = 3600  # 1小时
    
    def validate_file_upload(self, filename: str, file_path: str) -> tuple[bool, str]:
        """验证文件上传
        
        Args:
            filename: 文件名
            file_path: 文件路径
            
        Returns:
            (是否有效, 错误消息) 元组
        """
        # 验证文件名
        if not filename:
            return False, "文件名不能为空"
        
        # 验证文件类型
        if not validate_file_type(filename, self.allowed_file_extensions):
            return False, f"不支持的文件类型，允许的类型: {', '.join(self.allowed_file_extensions)}"
        
        # 验证文件大小
        if not validate_file_size(file_path, self.max_file_size_mb):
            return False, f"文件大小超过限制 ({self.max_file_size_mb}MB)"
        
        return True, ""
    
    def validate_password_strength(self, password: str) -> tuple[bool, str]:
        """验证密码强度
        
        Args:
            password: 密码
            
        Returns:
            (是否有效, 错误消息) 元组
        """
        if len(password) < self.password_min_length:
            return False, f"密码长度至少为 {self.password_min_length} 位"
        
        if not re.search(r'[A-Z]', password):
            return False, "密码必须包含大写字母"
        
        if not re.search(r'[a-z]', password):
            return False, "密码必须包含小写字母"
        
        if not re.search(r'\d', password):
            return False, "密码必须包含数字"
        
        return True, ""