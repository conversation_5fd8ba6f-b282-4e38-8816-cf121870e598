import sqlite3
import json
import os
from typing import Any, Dict, List, Optional, Union
from pathlib import Path
from contextlib import contextmanager
from loguru import logger


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, db_path: str = "data/app.db"):
        self.db_path = db_path
        self._ensure_database_directory()
        self._initialize_database()
    
    def _ensure_database_directory(self):
        """确保数据库目录存在"""
        db_dir = Path(self.db_path).parent
        db_dir.mkdir(parents=True, exist_ok=True)
    
    def _initialize_database(self):
        """初始化数据库连接"""
        # 只确保数据库文件存在，表结构由迁移系统管理
        if not os.path.exists(self.db_path):
            # 创建数据库文件
            with sqlite3.connect(self.db_path) as conn:
                pass
            logger.info(f"数据库文件已创建: {self.db_path}")
        else:
            logger.info(f"数据库文件已存在: {self.db_path}")
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接（上下文管理器）"""
        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row  # 使结果可以通过列名访问
            yield conn
        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"数据库操作错误: {e}")
            raise
        finally:
            if conn:
                conn.close()
    
    def execute_query(
        self,
        query: str,
        params: Optional[tuple] = None,
        fetch_one: bool = False,
        fetch_all: bool = True
    ) -> Union[List[Dict], Dict, None]:
        """执行查询
        
        Args:
            query: SQL查询语句
            params: 查询参数
            fetch_one: 是否只获取一条记录
            fetch_all: 是否获取所有记录
            
        Returns:
            查询结果
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            if fetch_one:
                row = cursor.fetchone()
                return dict(row) if row else None
            elif fetch_all:
                rows = cursor.fetchall()
                return [dict(row) for row in rows]
            else:
                return None
    
    def execute_transaction(self, operations: List[tuple]) -> bool:
        """执行事务
        
        Args:
            operations: 操作列表，每个元素为(query, params)元组
            
        Returns:
            是否执行成功
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                for query, params in operations:
                    if params:
                        cursor.execute(query, params)
                    else:
                        cursor.execute(query)
                
                conn.commit()
                return True
        except Exception as e:
            logger.error(f"事务执行失败: {e}")
            return False
    
    def insert_record(self, table: str, data: Dict[str, Any]) -> bool:
        """插入记录
        
        Args:
            table: 表名
            data: 数据字典
            
        Returns:
            是否插入成功
        """
        try:
            # 处理JSON字段
            processed_data = {}
            for key, value in data.items():
                if isinstance(value, (dict, list)):
                    processed_data[key] = json.dumps(value, ensure_ascii=False)
                else:
                    processed_data[key] = value
            
            columns = ', '.join(processed_data.keys())
            placeholders = ', '.join(['?' for _ in processed_data])
            query = f"INSERT INTO {table} ({columns}) VALUES ({placeholders})"
            
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query, list(processed_data.values()))
                conn.commit()
                return True
        except Exception as e:
            logger.error(f"插入记录失败: {e}")
            return False
    
    def update_record(self, table: str, record_id: str, data: Dict[str, Any]) -> bool:
        """更新记录
        
        Args:
            table: 表名
            record_id: 记录ID
            data: 更新数据
            
        Returns:
            是否更新成功
        """
        try:
            # 处理JSON字段
            processed_data = {}
            for key, value in data.items():
                if isinstance(value, (dict, list)):
                    processed_data[key] = json.dumps(value, ensure_ascii=False)
                else:
                    processed_data[key] = value
            
            # 添加更新时间
            processed_data['updated_at'] = 'CURRENT_TIMESTAMP'
            
            set_clause = ', '.join([f"{key} = ?" for key in processed_data.keys()])
            query = f"UPDATE {table} SET {set_clause} WHERE id = ?"
            
            params = list(processed_data.values()) + [record_id]
            
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query, params)
                conn.commit()
                return cursor.rowcount > 0
        except Exception as e:
            logger.error(f"更新记录失败: {e}")
            return False
    
    def delete_record(self, table: str, record_id: str) -> bool:
        """删除记录
        
        Args:
            table: 表名
            record_id: 记录ID
            
        Returns:
            是否删除成功
        """
        try:
            query = f"DELETE FROM {table} WHERE id = ?"
            
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query, (record_id,))
                conn.commit()
                return cursor.rowcount > 0
        except Exception as e:
            logger.error(f"删除记录失败: {e}")
            return False
    
    def get_record(self, table: str, record_id: str) -> Optional[Dict[str, Any]]:
        """获取单条记录
        
        Args:
            table: 表名
            record_id: 记录ID
            
        Returns:
            记录数据
        """
        query = f"SELECT * FROM {table} WHERE id = ?"
        result = self.execute_query(query, (record_id,), fetch_one=True)
        
        if result:
            # 解析JSON字段
            for key, value in result.items():
                if isinstance(value, str) and key in ['config', 'fields', 'components', 'layout', 'nodes', 'edges', 'logs', 'result', 'template_data', 'tags']:
                    try:
                        result[key] = json.loads(value)
                    except (json.JSONDecodeError, TypeError):
                        pass
        
        return result
    
    def get_records(
        self,
        table: str,
        where_clause: Optional[str] = None,
        params: Optional[tuple] = None,
        order_by: Optional[str] = None,
        limit: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """获取多条记录
        
        Args:
            table: 表名
            where_clause: WHERE子句
            params: 查询参数
            order_by: 排序字段
            limit: 限制数量
            
        Returns:
            记录列表
        """
        query = f"SELECT * FROM {table}"
        
        if where_clause:
            query += f" WHERE {where_clause}"
        
        if order_by:
            query += f" ORDER BY {order_by}"
        
        if limit:
            query += f" LIMIT {limit}"
        
        results = self.execute_query(query, params)
        
        # 解析JSON字段
        for result in results:
            for key, value in result.items():
                if isinstance(value, str) and key in ['config', 'fields', 'components', 'layout', 'nodes', 'edges', 'logs', 'result', 'template_data', 'tags']:
                    try:
                        result[key] = json.loads(value)
                    except (json.JSONDecodeError, TypeError):
                        pass
        
        return results
    
    def backup_database(self, backup_path: str) -> bool:
        """备份数据库
        
        Args:
            backup_path: 备份文件路径
            
        Returns:
            是否备份成功
        """
        try:
            import shutil
            shutil.copy2(self.db_path, backup_path)
            logger.info(f"数据库备份成功: {backup_path}")
            return True
        except Exception as e:
            logger.error(f"数据库备份失败: {e}")
            return False
    
    def restore_database(self, backup_path: str) -> bool:
        """恢复数据库
        
        Args:
            backup_path: 备份文件路径
            
        Returns:
            是否恢复成功
        """
        try:
            import shutil
            shutil.copy2(backup_path, self.db_path)
            logger.info(f"数据库恢复成功: {backup_path}")
            return True
        except Exception as e:
            logger.error(f"数据库恢复失败: {e}")
            return False
    
    def get_database_stats(self) -> Dict[str, Any]:
        """获取数据库统计信息
        
        Returns:
            统计信息字典
        """
        stats = {}
        
        tables = [
            'projects', 'protocols', 'interfaces', 'flows',
            'flow_executions', 'serial_data_history', 'project_templates'
        ]
        
        for table in tables:
            count_query = f"SELECT COUNT(*) as count FROM {table}"
            result = self.execute_query(count_query, fetch_one=True)
            stats[f"{table}_count"] = result['count'] if result else 0
        
        # 数据库文件大小
        try:
            stats['database_size'] = os.path.getsize(self.db_path)
        except OSError:
            stats['database_size'] = 0
        
        return stats


# 全局数据库管理器实例
_db_manager = None


def get_database_connection():
    """获取数据库连接"""
    global _db_manager
    if _db_manager is None:
        _db_manager = DatabaseManager()
    return _db_manager.get_connection()


def execute_query(
    query: str,
    params: Optional[tuple] = None,
    fetch_one: bool = False,
    fetch_all: bool = True
) -> Union[List[Dict], Dict, None]:
    """执行查询（便捷函数）"""
    global _db_manager
    if _db_manager is None:
        _db_manager = DatabaseManager()
    return _db_manager.execute_query(query, params, fetch_one, fetch_all)


def execute_transaction(operations: List[tuple]) -> bool:
    """执行事务（便捷函数）"""
    global _db_manager
    if _db_manager is None:
        _db_manager = DatabaseManager()
    return _db_manager.execute_transaction(operations)


def get_db_manager() -> DatabaseManager:
    """获取数据库管理器实例"""
    global _db_manager
    if _db_manager is None:
        _db_manager = DatabaseManager()
    return _db_manager