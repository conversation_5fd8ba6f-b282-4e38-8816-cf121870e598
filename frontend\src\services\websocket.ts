class WebSocketService {
  private ws: WebSocket | null = null
  private url: string
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectInterval = 3000
  private listeners: Map<string, Function[]> = new Map()
  private isConnecting = false

  constructor(url: string = 'ws://localhost:8000/ws') {
    this.url = url
  }

  // 连接WebSocket
  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        resolve()
        return
      }

      if (this.isConnecting) {
        reject(new Error('WebSocket is already connecting'))
        return
      }

      this.isConnecting = true

      try {
        this.ws = new WebSocket(this.url)

        this.ws.onopen = () => {
          console.log('WebSocket连接已建立')
          this.isConnecting = false
          this.reconnectAttempts = 0
          this.emit('connected')
          resolve()
        }

        this.ws.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data)
            this.handleMessage(data)
          } catch (error) {
            console.error('解析WebSocket消息失败:', error)
          }
        }

        this.ws.onclose = (event) => {
          console.log('WebSocket连接已关闭:', event.code, event.reason)
          this.isConnecting = false
          this.emit('disconnected', { code: event.code, reason: event.reason })
          
          // 如果不是主动关闭，尝试重连
          if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.scheduleReconnect()
          }
        }

        this.ws.onerror = (error) => {
          console.error('WebSocket错误:', error)
          this.isConnecting = false
          this.emit('error', error)
          reject(error)
        }
      } catch (error) {
        this.isConnecting = false
        reject(error)
      }
    })
  }

  // 断开连接
  disconnect() {
    if (this.ws) {
      this.ws.close(1000, 'Client disconnect')
      this.ws = null
    }
  }

  // 发送消息
  send(type: string, data: any) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      const message = {
        type,
        data,
        timestamp: new Date().toISOString(),
      }
      this.ws.send(JSON.stringify(message))
    } else {
      console.warn('WebSocket未连接，无法发送消息')
    }
  }

  // 处理接收到的消息
  private handleMessage(message: any) {
    const { type, data } = message
    this.emit(type, data)
  }

  // 添加事件监听器
  on(event: string, callback: Function) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, [])
    }
    this.listeners.get(event)!.push(callback)
  }

  // 移除事件监听器
  off(event: string, callback?: Function) {
    if (!this.listeners.has(event)) return

    if (callback) {
      const callbacks = this.listeners.get(event)!
      const index = callbacks.indexOf(callback)
      if (index > -1) {
        callbacks.splice(index, 1)
      }
    } else {
      this.listeners.delete(event)
    }
  }

  // 触发事件
  private emit(event: string, data?: any) {
    if (this.listeners.has(event)) {
      this.listeners.get(event)!.forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error(`事件监听器执行错误 (${event}):`, error)
        }
      })
    }
  }

  // 安排重连
  private scheduleReconnect() {
    this.reconnectAttempts++
    console.log(`尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`)
    
    setTimeout(() => {
      this.connect().catch(error => {
        console.error('重连失败:', error)
      })
    }, this.reconnectInterval)
  }

  // 获取连接状态
  get isConnected(): boolean {
    return this.ws !== null && this.ws.readyState === WebSocket.OPEN
  }

  // 获取连接状态文本
  get connectionState(): string {
    if (!this.ws) return 'CLOSED'
    
    switch (this.ws.readyState) {
      case WebSocket.CONNECTING:
        return 'CONNECTING'
      case WebSocket.OPEN:
        return 'OPEN'
      case WebSocket.CLOSING:
        return 'CLOSING'
      case WebSocket.CLOSED:
        return 'CLOSED'
      default:
        return 'UNKNOWN'
    }
  }
}

// 创建全局WebSocket实例
const wsService = new WebSocketService()

// 串口数据相关的WebSocket方法
export const serialWebSocket = {
  // 连接WebSocket
  connect: () => wsService.connect(),
  
  // 断开连接
  disconnect: () => wsService.disconnect(),
  
  // 监听串口数据
  onSerialData: (callback: (data: any) => void) => {
    wsService.on('serial_data', callback)
  },
  
  // 监听串口状态变化
  onSerialStatus: (callback: (status: any) => void) => {
    wsService.on('serial_status', callback)
  },
  
  // 监听连接状态
  onConnectionChange: (callback: (connected: boolean) => void) => {
    wsService.on('connected', () => callback(true))
    wsService.on('disconnected', () => callback(false))
  },
  
  // 移除监听器
  off: (event: string, callback?: Function) => {
    wsService.off(event, callback)
  },
  
  // 获取连接状态
  get isConnected() {
    return wsService.isConnected
  },
}

// 流程执行相关的WebSocket方法
export const flowWebSocket = {
  // 监听流程执行状态
  onFlowStatus: (callback: (status: any) => void) => {
    wsService.on('flow_status', callback)
  },
  
  // 监听流程执行日志
  onFlowLog: (callback: (log: any) => void) => {
    wsService.on('flow_log', callback)
  },
  
  // 发送流程控制命令
  sendFlowCommand: (command: string, data?: any) => {
    wsService.send('flow_command', { command, data })
  },
}

// 系统通知相关的WebSocket方法
export const notificationWebSocket = {
  // 监听系统通知
  onNotification: (callback: (notification: any) => void) => {
    wsService.on('notification', callback)
  },
  
  // 监听系统状态
  onSystemStatus: (callback: (status: any) => void) => {
    wsService.on('system_status', callback)
  },
}

export default wsService