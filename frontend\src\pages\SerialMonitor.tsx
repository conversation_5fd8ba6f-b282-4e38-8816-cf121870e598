import React, { useState, useEffect, useRef } from 'react'
import { Card, Row, Col, Button, Space, Input, Select, Switch, Divider, Tag, message } from 'antd'
import { 
  PlayCircleOutlined, 
  PauseCircleOutlined, 
  ClearOutlined, 
  DownloadOutlined,
  SendOutlined
} from '@ant-design/icons'
import { useSerialStore } from '../stores/serialStore'

const { TextArea } = Input
const { Option } = Select

interface LogEntry {
  id: string
  timestamp: string
  direction: 'send' | 'receive'
  data: string
  format: 'hex' | 'ascii'
}

const SerialMonitor: React.FC = () => {
  const {
    isConnected,
    selectedPort,
    config,
    availablePorts,
    connectSerial,
    disconnectSerial,
    sendData,
    refreshPorts,
  } = useSerialStore()

  const [logs, setLogs] = useState<LogEntry[]>([])
  const [sendText, setSendText] = useState('')
  const [sendFormat, setSendFormat] = useState<'hex' | 'ascii'>('ascii')
  const [displayFormat, setDisplayFormat] = useState<'hex' | 'ascii'>('ascii')
  const [autoScroll, setAutoScroll] = useState(true)
  const [isMonitoring, setIsMonitoring] = useState(false)
  const [filters, setFilters] = useState({
    showSend: true,
    showReceive: true,
    keyword: '',
  })
  
  const logContainerRef = useRef<HTMLDivElement>(null)

  // 模拟接收数据
  useEffect(() => {
    if (isConnected && isMonitoring) {
      const interval = setInterval(() => {
        const mockData = [
          'AT+OK',
          'TEMP:25.6',
          'VOLT:3.3V',
          'STATUS:READY',
          '01 02 03 04 FF',
        ]
        
        const randomData = mockData[Math.floor(Math.random() * mockData.length)]
        addLog('receive', randomData, 'ascii')
      }, 2000 + Math.random() * 3000)

      return () => clearInterval(interval)
    }
  }, [isConnected, isMonitoring])

  // 自动滚动到底部
  useEffect(() => {
    if (autoScroll && logContainerRef.current) {
      logContainerRef.current.scrollTop = logContainerRef.current.scrollHeight
    }
  }, [logs, autoScroll])

  const addLog = (direction: 'send' | 'receive', data: string, format: 'hex' | 'ascii') => {
    const newLog: LogEntry = {
      id: Date.now().toString() + Math.random(),
      timestamp: new Date().toLocaleTimeString(),
      direction,
      data,
      format,
    }
    
    setLogs(prev => [...prev, newLog])
  }

  const handleSend = () => {
    if (!isConnected) {
      message.warning('请先连接串口')
      return
    }
    
    if (!sendText.trim()) {
      message.warning('请输入要发送的数据')
      return
    }

    try {
      sendData(sendText)
      addLog('send', sendText, sendFormat)
      setSendText('')
      message.success('数据发送成功')
    } catch (error) {
      message.error('数据发送失败')
    }
  }

  const handleConnect = async () => {
    if (!selectedPort) {
      message.warning('请选择串口')
      return
    }

    try {
      await connectSerial()
      message.success('串口连接成功')
      setIsMonitoring(true)
    } catch (error) {
      message.error('串口连接失败')
    }
  }

  const handleDisconnect = () => {
    disconnectSerial()
    setIsMonitoring(false)
    message.info('串口已断开')
  }

  const clearLogs = () => {
    setLogs([])
  }

  const exportLogs = () => {
    const logText = logs
      .filter(log => {
        if (!filters.showSend && log.direction === 'send') return false
        if (!filters.showReceive && log.direction === 'receive') return false
        if (filters.keyword && !log.data.toLowerCase().includes(filters.keyword.toLowerCase())) return false
        return true
      })
      .map(log => `[${log.timestamp}] ${log.direction.toUpperCase()}: ${log.data}`)
      .join('\n')
    
    const blob = new Blob([logText], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `serial_log_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`
    a.click()
    URL.revokeObjectURL(url)
  }

  const formatData = (data: string, format: 'hex' | 'ascii') => {
    if (format === 'hex') {
      // 如果是ASCII，转换为HEX显示
      return data.split('').map(char => 
        char.charCodeAt(0).toString(16).padStart(2, '0').toUpperCase()
      ).join(' ')
    }
    return data
  }

  const filteredLogs = logs.filter(log => {
    if (!filters.showSend && log.direction === 'send') return false
    if (!filters.showReceive && log.direction === 'receive') return false
    if (filters.keyword && !log.data.toLowerCase().includes(filters.keyword.toLowerCase())) return false
    return true
  })

  return (
    <div style={{ padding: '0 24px' }}>
      <Row gutter={16}>
        {/* 连接控制面板 */}
        <Col span={8}>
          <Card title="串口连接" size="small" style={{ marginBottom: 16 }}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <div style={{ marginBottom: 8 }}>串口选择:</div>
                <Space style={{ width: '100%' }}>
                  <Select
                    style={{ flex: 1 }}
                    placeholder="选择串口"
                    value={selectedPort}
                    onChange={(value) => useSerialStore.getState().setSelectedPort(value)}
                  >
                    {availablePorts.map(port => (
                      <Option key={port.path} value={port.path}>{port.path}</Option>
                    ))}
                  </Select>
                  <Button onClick={refreshPorts}>刷新</Button>
                </Space>
              </div>
              
              <div>
                <div style={{ marginBottom: 8 }}>波特率:</div>
                <Select
                  style={{ width: '100%' }}
                  value={config.baudRate}
                  onChange={(value) => useSerialStore.getState().setConfig({ ...config, baudRate: value })}
                >
                  <Option value={9600}>9600</Option>
                  <Option value={19200}>19200</Option>
                  <Option value={38400}>38400</Option>
                  <Option value={57600}>57600</Option>
                  <Option value={115200}>115200</Option>
                </Select>
              </div>

              <div>
                <Space style={{ width: '100%', justifyContent: 'space-between' }}>
                  <span>数据位:</span>
                  <Select
                    style={{ width: 80 }}
                    value={config.dataBits}
                    onChange={(value) => useSerialStore.getState().setConfig({ ...config, dataBits: value })}
                  >
                    <Option value={7}>7</Option>
                    <Option value={8}>8</Option>
                  </Select>
                </Space>
              </div>

              <div>
                <Space style={{ width: '100%', justifyContent: 'space-between' }}>
                  <span>停止位:</span>
                  <Select
                    style={{ width: 80 }}
                    value={config.stopBits}
                    onChange={(value) => useSerialStore.getState().setConfig({ ...config, stopBits: value })}
                  >
                    <Option value={1}>1</Option>
                    <Option value={2}>2</Option>
                  </Select>
                </Space>
              </div>

              <div>
                <Space style={{ width: '100%', justifyContent: 'space-between' }}>
                  <span>校验位:</span>
                  <Select
                    style={{ width: 80 }}
                    value={config.parity}
                    onChange={(value) => useSerialStore.getState().setConfig({ ...config, parity: value })}
                  >
                    <Option value="none">无</Option>
                    <Option value="even">偶</Option>
                    <Option value="odd">奇</Option>
                  </Select>
                </Space>
              </div>

              <Divider style={{ margin: '12px 0' }} />
              
              <Space style={{ width: '100%' }}>
                {!isConnected ? (
                  <Button 
                    type="primary" 
                    icon={<PlayCircleOutlined />}
                    onClick={handleConnect}
                    block
                  >
                    连接
                  </Button>
                ) : (
                  <Button 
                    danger
                    icon={<PauseCircleOutlined />}
                    onClick={handleDisconnect}
                    block
                  >
                    断开
                  </Button>
                )}
              </Space>
              
              <div style={{ textAlign: 'center' }}>
                <Tag color={isConnected ? 'success' : 'default'}>
                  {isConnected ? '已连接' : '未连接'}
                </Tag>
              </div>
            </Space>
          </Card>

          {/* 数据发送面板 */}
          <Card title="数据发送" size="small">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <div style={{ marginBottom: 8 }}>发送格式:</div>
                <Select
                  style={{ width: '100%' }}
                  value={sendFormat}
                  onChange={setSendFormat}
                >
                  <Option value="ascii">ASCII</Option>
                  <Option value="hex">HEX</Option>
                </Select>
              </div>
              
              <TextArea
                rows={4}
                placeholder={sendFormat === 'hex' ? '输入十六进制数据，如: 01 02 03' : '输入ASCII文本'}
                value={sendText}
                onChange={(e) => setSendText(e.target.value)}
              />
              
              <Button 
                type="primary" 
                icon={<SendOutlined />}
                onClick={handleSend}
                disabled={!isConnected}
                block
              >
                发送
              </Button>
            </Space>
          </Card>
        </Col>
        
        {/* 数据监控面板 */}
        <Col span={16}>
          <Card 
            title="数据监控" 
            extra={
              <Space>
                <span>显示格式:</span>
                <Select
                  size="small"
                  value={displayFormat}
                  onChange={setDisplayFormat}
                  style={{ width: 80 }}
                >
                  <Option value="ascii">ASCII</Option>
                  <Option value="hex">HEX</Option>
                </Select>
                <Switch
                  size="small"
                  checked={autoScroll}
                  onChange={setAutoScroll}
                  checkedChildren="自动滚动"
                  unCheckedChildren="手动滚动"
                />
                <Button size="small" icon={<ClearOutlined />} onClick={clearLogs}>
                  清空
                </Button>
                <Button size="small" icon={<DownloadOutlined />} onClick={exportLogs}>
                  导出
                </Button>
              </Space>
            }
          >
            {/* 过滤器 */}
            <div style={{ marginBottom: 16, padding: '12px', background: '#fafafa', borderRadius: 6 }}>
              <Space>
                <span>过滤:</span>
                <Switch
                  size="small"
                  checked={filters.showSend}
                  onChange={(checked) => setFilters({ ...filters, showSend: checked })}
                  checkedChildren="发送"
                  unCheckedChildren="发送"
                />
                <Switch
                  size="small"
                  checked={filters.showReceive}
                  onChange={(checked) => setFilters({ ...filters, showReceive: checked })}
                  checkedChildren="接收"
                  unCheckedChildren="接收"
                />
                <Input
                  size="small"
                  placeholder="关键字过滤"
                  value={filters.keyword}
                  onChange={(e) => setFilters({ ...filters, keyword: e.target.value })}
                  style={{ width: 150 }}
                />
                <span style={{ color: '#666' }}>共 {filteredLogs.length} 条记录</span>
              </Space>
            </div>
            
            {/* 日志显示区域 */}
            <div 
              ref={logContainerRef}
              style={{ 
                height: 500, 
                border: '1px solid #d9d9d9', 
                borderRadius: 6,
                padding: 12,
                backgroundColor: '#000',
                color: '#fff',
                fontFamily: 'Consolas, Monaco, monospace',
                fontSize: 12,
                overflow: 'auto'
              }}
            >
              {filteredLogs.length === 0 ? (
                <div style={{ color: '#666', textAlign: 'center', marginTop: 50 }}>
                  暂无数据
                </div>
              ) : (
                filteredLogs.map(log => (
                  <div key={log.id} style={{ marginBottom: 4 }}>
                    <span style={{ color: '#666' }}>[{log.timestamp}]</span>
                    <span style={{ 
                      color: log.direction === 'send' ? '#52c41a' : '#1890ff',
                      marginLeft: 8,
                      marginRight: 8
                    }}>
                      {log.direction === 'send' ? '→' : '←'}
                    </span>
                    <span style={{ color: '#fff' }}>
                      {formatData(log.data, displayFormat)}
                    </span>
                  </div>
                ))
              )}
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  )
}

export default SerialMonitor