# 开发环境 Dockerfile
# 用于开发环境的容器化部署

FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y --no-install-recommends \
    gcc \
    libc6-dev \
    make \
    curl \
    git \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 安装Python依赖
COPY backend/requirements.txt ./backend/
RUN pip install --no-cache-dir -r backend/requirements.txt

# 安装开发工具
RUN pip install --no-cache-dir \
    black \
    isort \
    flake8 \
    mypy \
    pytest \
    pytest-asyncio \
    pytest-cov

# 创建必要的目录
RUN mkdir -p data logs uploads backups

# 设置环境变量
ENV ENVIRONMENT=development
ENV DEBUG=true
ENV HOST=0.0.0.0
ENV PORT=8000
ENV LOG_LEVEL=DEBUG
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["python", "start.py", "--env", "development", "--reload"]