import os
import time
import sqlite3
import importlib.util
from pathlib import Path
from typing import List, Dict, Any, Optional
from loguru import logger

from ..utils.database import get_database_connection


def get_migrations_path() -> Path:
    """获取迁移文件目录路径"""
    return Path("migrations")


def ensure_migrations_table():
    """确保迁移记录表存在"""
    with get_database_connection() as conn:
        cursor = conn.cursor()
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS migrations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        conn.commit()


def get_applied_migrations() -> List[str]:
    """获取已应用的迁移列表"""
    ensure_migrations_table()
    
    with get_database_connection() as conn:
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM migrations ORDER BY id")
        return [row['name'] for row in cursor.fetchall()]


def record_migration(name: str):
    """记录已应用的迁移"""
    with get_database_connection() as conn:
        cursor = conn.cursor()
        cursor.execute("INSERT INTO migrations (name) VALUES (?)", (name,))
        conn.commit()


def get_pending_migrations() -> List[str]:
    """获取待应用的迁移列表"""
    migrations_path = get_migrations_path()
    if not migrations_path.exists():
        migrations_path.mkdir(parents=True, exist_ok=True)
        return []
    
    # 获取所有迁移文件
    migration_files = sorted([f.stem for f in migrations_path.glob("*.py")])
    
    # 获取已应用的迁移
    applied_migrations = get_applied_migrations()
    
    # 返回未应用的迁移
    return [m for m in migration_files if m not in applied_migrations]


def run_migration(migration_name: str) -> bool:
    """运行单个迁移
    
    Args:
        migration_name: 迁移名称
        
    Returns:
        是否成功
    """
    migrations_path = get_migrations_path()
    migration_file = migrations_path / f"{migration_name}.py"
    
    if not migration_file.exists():
        logger.error(f"迁移文件不存在: {migration_file}")
        return False
    
    try:
        # 动态加载迁移模块
        spec = importlib.util.spec_from_file_location(migration_name, migration_file)
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        
        # 执行迁移
        if hasattr(module, 'upgrade'):
            logger.info(f"应用迁移: {migration_name}")
            module.upgrade()
            record_migration(migration_name)
            return True
        else:
            logger.error(f"迁移文件缺少upgrade函数: {migration_file}")
            return False
    except Exception as e:
        logger.error(f"迁移执行失败 {migration_name}: {e}")
        return False


def run_migrations() -> Dict[str, Any]:
    """运行所有待应用的迁移
    
    Returns:
        迁移结果统计
    """
    pending_migrations = get_pending_migrations()
    
    if not pending_migrations:
        logger.info("没有待应用的迁移")
        return {"success": True, "applied": 0, "failed": 0, "pending": 0}
    
    logger.info(f"发现 {len(pending_migrations)} 个待应用的迁移")
    
    applied = 0
    failed = 0
    
    for migration in pending_migrations:
        if run_migration(migration):
            applied += 1
        else:
            failed += 1
            # 迁移失败时停止
            break
    
    result = {
        "success": failed == 0,
        "applied": applied,
        "failed": failed,
        "pending": len(pending_migrations) - applied - failed
    }
    
    logger.info(f"迁移完成: {result}")
    return result


def create_migration(name: str, description: Optional[str] = None) -> str:
    """创建新的迁移文件
    
    Args:
        name: 迁移名称
        description: 迁移描述
        
    Returns:
        创建的迁移文件路径
    """
    migrations_path = get_migrations_path()
    migrations_path.mkdir(parents=True, exist_ok=True)
    
    # 生成迁移文件名
    timestamp = int(time.time())
    filename = f"{timestamp}_{name}.py"
    file_path = migrations_path / filename
    
    # 迁移文件模板
    template = f"""
{description or f'迁移: {name}'}

from ..utils.database import get_database_connection


def upgrade():
    \"\"\"应用迁移\"\"\"
    with get_database_connection() as conn:
        cursor = conn.cursor()
        
        # TODO: 在此处添加迁移SQL语句
        # 例如: cursor.execute(\"ALTER TABLE users ADD COLUMN email TEXT\")
        
        conn.commit()


def downgrade():
    \"\"\"回滚迁移\"\"\"
    with get_database_connection() as conn:
        cursor = conn.cursor()
        
        # TODO: 在此处添加回滚SQL语句
        # 例如: cursor.execute(\"ALTER TABLE users DROP COLUMN email\")
        
        conn.commit()
"""
    
    # 写入迁移文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(template)
    
    logger.info(f"创建迁移文件: {file_path}")
    return str(file_path)


def rollback_migration(migration_name: Optional[str] = None) -> bool:
    """回滚迁移
    
    Args:
        migration_name: 要回滚的迁移名称,如果为None则回滚最后一个迁移
        
    Returns:
        是否成功
    """
    applied_migrations = get_applied_migrations()
    
    if not applied_migrations:
        logger.info("没有可回滚的迁移")
        return False
    
    # 确定要回滚的迁移
    if migration_name is None:
        migration_name = applied_migrations[-1]
    elif migration_name not in applied_migrations:
        logger.error(f"迁移 {migration_name} 未应用，无法回滚")
        return False
    
    migrations_path = get_migrations_path()
    migration_file = migrations_path / f"{migration_name}.py"
    
    if not migration_file.exists():
        logger.error(f"迁移文件不存在: {migration_file}")
        return False
    
    try:
        # 动态加载迁移模块
        spec = importlib.util.spec_from_file_location(migration_name, migration_file)
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        
        # 执行回滚
        if hasattr(module, 'downgrade'):
            logger.info(f"回滚迁移: {migration_name}")
            module.downgrade()
            
            # 从迁移记录中删除
            with get_database_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("DELETE FROM migrations WHERE name = ?", (migration_name,))
                conn.commit()
            
            return True
        else:
            logger.error(f"迁移文件缺少downgrade函数: {migration_file}")
            return False
    except Exception as e:
        logger.error(f"迁移回滚失败 {migration_name}: {e}")
        return False


def get_migration_status() -> List[Dict[str, Any]]:
    """获取所有迁移的状态
    
    Returns:
        迁移状态列表
    """
    migrations_path = get_migrations_path()
    if not migrations_path.exists():
        migrations_path.mkdir(parents=True, exist_ok=True)
        return []
    
    # 获取所有迁移文件
    migration_files = sorted([f.stem for f in migrations_path.glob("*.py")])
    
    # 获取已应用的迁移及其应用时间
    applied_migrations = {}
    with get_database_connection() as conn:
        cursor = conn.cursor()
        cursor.execute("SELECT name, applied_at FROM migrations ORDER BY id")
        for row in cursor.fetchall():
            applied_migrations[row['name']] = row['applied_at']
    
    # 构建状态列表
    status_list = []
    for migration in migration_files:
        status_list.append({
            "name": migration,
            "applied": migration in applied_migrations,
            "applied_at": applied_migrations.get(migration),
            "file_path": str(migrations_path / f"{migration}.py")
        })
    
    return status_list