# 项目结构文档

本文档旨在详细说明 `EC_Test_Create` 项目的整体架构、文件组织方式以及每个模块的核心功能。该项目是一个前后端分离的高度可配置的串口通信与自动化测试平台。

## 根目录文件说明

| 文件/目录 | 描述 |
| --- | --- |
| `.gitignore` | 指定 Git 版本控制中应忽略的文件和目录，如依赖项、日志文件和构建产物。 |
| `.trae/` | Trae AI 代理的配置文件目录。 |
| `API_DOCUMENTATION.md` | 后端 API 的详细文档，描述了每个端点的功能、参数和响应格式。 |
| `CHANGELOG.md` | 项目的版本历史和变更记录。 |
| `Dockerfile` | 用于构建生产环境的 Docker 镜像的配置文件。 |
| `Dockerfile.dev` | 用于构建开发环境的 Docker 镜像的配置文件，通常包含热重载等开发工具。 |
| `LICENSE` | 项目的开源许可证文件。 |
| `MEMORY_POINTS.md` | 项目开发过程中的关键记忆点和决策记录。 |
| `Makefile` | 包含一系列用于简化常见开发任务（如构建、测试、部署）的命令。 |
| `README.md` | 项目的入口文档，提供项目简介、安装指南和快速上手说明。 |
| `backend/` | 后端应用代码目录。 |
| `docker-compose.dev.yml` | 使用 `Dockerfile.dev` 编排开发环境容器的 Docker Compose 文件。 |
| `docker-compose.yml` | 编排生产环境容器的 Docker Compose 文件。 |
| `docs/` | 存放项目相关的所有文档。 |
| `frontend/` | 前端应用代码目录。 |
| `migrations/` | 数据库迁移脚本目录，由 Alembic 管理。 |
| `shared/` | 存放前后端共享的代码或类型定义（如果项目需要）。 |
| `start.py` | 项目的统一启动脚本，可以同时启动前后端服务。 |

---

## 后端 (`backend`)

后端采用 Python 的 FastAPI 框架构建，负责处理所有业务逻辑、数据库交互和硬件通信。

| 文件/目录 | 描述 |
| --- | --- |
| `.env.example` | 环境变量配置文件的示例，包含了项目运行所需的所有环境变量。 |
| `main.py` | **应用主入口**。负责初始化 FastAPI 应用、挂载路由、配置中间件和启动服务器。 |
| `pyproject.toml` | Python 项目的配置文件，用于定义项目元数据和依赖管理（如使用 Poetry 或 Flit）。 |
| `pytest.ini` | `pytest` 测试框架的配置文件。 |
| `requirements.txt` | 项目的 Python 依赖列表。 |
| `websocket_manager.py` | 封装了 WebSocket 连接管理和消息广播的逻辑。 |

### `core/` - 核心配置

| 文件 | 描述 |
| --- | --- |
| `config.py` | 加载和管理应用的所有配置，如数据库连接字符串、密钥等。 |
| `websocket.py` | 提供了 WebSocket 连接管理的相关工具函数或类。 |

### `database/` - 数据库

| 文件 | 描述 |
| --- | --- |
| `database.py` | 设置数据库连接（SQLAlchemy），提供数据库会话（Session）的依赖注入。 |
| `migrations.py` | 数据库迁移相关的配置或脚本。 |
| `seeds.py` | 用于向数据库填充初始数据（种子数据）的脚本。 |

### `models/` - 数据模型

| 文件 | 描述 |
| --- | --- |
| `base.py` | 定义了所有数据模型（ORM 类）的基类，通常与 SQLAlchemy 的 `declarative_base` 相关。 |

### `routers/` - API 路由

此目录包含了所有的 API 端点定义，每个文件对应一个资源模块。

| 文件 | 描述 |
| --- | --- |
| `projects.py` | 处理项目管理的路由，如创建、导入、导出项目。 |
| `protocols.py` | 处理通信协议管理的路由。 |
| `serial.py` | 处理串口通信的路由，如连接、断开、收发数据。 |
| `flows.py` | 处理自动化工作流程（Flow）的路由。 |
| `interfaces.py` | 处理自定义监控界面设计的路由。 |

### `services/` - 业务逻辑

服务层封装了核心的业务逻辑，将路由处理与具体的实现解耦。

| 文件 | 描述 |
| --- | --- |
| `project_service.py` | 封装了项目管理的业务逻辑。 |
| `protocol_service.py` | 封装了协议定义和解析的业务逻辑。 |
| `serial_service.py` | 封装了与串口硬件交互的业务逻辑。 |
| `flow_service.py` | 封装了流程引擎的执行和管理逻辑。 |
| `interface_service.py` | 封装了界面设计的业务逻辑。 |

### `utils/` - 通用工具

| 文件 | 描述 |
| --- | --- |
| `converters.py` | 提供数据格式转换的工具函数，如十六进制与字符串互转。 |
| `database.py` | 提供与数据库交互的通用辅助函数。 |
| `formatters.py` | 提供数据格式化的工具函数。 |
| `logger.py` | 配置和管理应用的日志记录器。 |
| `security.py` | 提供安全相关的工具函数，如密码哈希、令牌生成等。 |
| `validators.py` | 提供数据验证的工具函数。 |

---

## 前端 (`frontend`)

前端是一个基于 React、Vite 和 TypeScript 构建的单页应用（SPA），UI 库为 Ant Design。

| 文件/目录 | 描述 |
| --- | --- |
| `index.html` | SPA 的主 HTML 文件，是应用的入口。 |
| `package.json` | 定义了项目的元数据、依赖项和脚本命令。 |
| `package-lock.json` | 锁定项目依赖的具体版本，确保环境一致性。 |
| `tsconfig.json` | TypeScript 编译器的配置文件。 |
| `vite.config.ts` | Vite 构建工具的配置文件。 |

### `src/` - 源代码目录

| 文件/目录 | 描述 |
| --- | --- |
| `App.tsx` | **应用根组件**。负责设置路由、全局上下文和主题。 |
| `main.tsx` | **应用入口文件**。负责将 `App` 组件渲染到 DOM 中。 |
| `index.css` | 全局 CSS 样式文件。 |

### `src/assets/` - 静态资源

存放图片、字体等静态文件。

### `src/components/` - 可重用组件

存放整个应用中可以复用的 UI 组件。

| 文件 | 描述 |
| --- | --- |
| `Layout/MainLayout.tsx` | 定义了应用的主体布局，包括侧边栏、头部和内容区域。 |

### `src/pages/` - 页面组件

每个文件对应一个功能页面。

| 文件 | 描述 |
| --- | --- |
| `Dashboard.tsx` | 仪表盘页面，提供系统状态概览。 |
| `ProjectManagement.tsx` | 项目管理页面。 |
| `ProtocolConfig.tsx` | 通信协议配置页面。 |
| `SerialMonitor.tsx` | 串口监控与调试页面。 |
| `InterfaceDesign.tsx` | 拖放式监控界面设计器页面。 |
| `FlowEngine.tsx` | 可视化流程引擎编排页面。 |

### `src/services/` - API 服务

| 文件 | 描述 |
| --- | --- |
| `api.ts` | 使用 `axios` 封装所有对后端 RESTful API 的请求。 |
| `websocket.ts` | 封装 WebSocket 连接和事件处理。 |

### `src/stores/` - 状态管理

使用 Zustand 进行全局状态管理。

| 文件 | 描述 |
| --- | --- |
| `serialStore.ts` | 管理与串口通信相关的全局状态。 |

### `src/types/` - 类型定义

| 文件 | 描述 |
| --- | --- |
| `index.ts` | 存放整个项目中使用的 TypeScript 类型和接口定义。 |

### `src/utils/` - 工具函数

| 文件 | 描述 |
| --- | --- |
| `index.ts` | 提供前端应用中可复用的通用工具函数。 |