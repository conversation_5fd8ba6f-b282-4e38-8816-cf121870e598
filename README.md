# 串口通信测试工具

一个基于 FastAPI 和 React 的串口通信测试工具，支持协议配置、界面设计和流程引擎。

## 功能特性

- 🔌 **串口通信管理** - 支持多种串口参数配置，实时数据收发
- 📋 **协议配置** - 灵活的协议定义，支持多种数据格式
- 🎨 **界面设计** - 可视化界面设计器，拖拽式组件布局
- ⚙️ **流程引擎** - 自动化测试流程，支持条件判断和循环
- 📊 **项目管理** - 项目模板、版本控制、数据导入导出
- 🔄 **实时监控** - WebSocket 实时数据推送，性能监控
- 🛡️ **安全特性** - 数据加密、访问控制、审计日志

## 技术栈

### 后端
- **FastAPI** - 现代化的 Python Web 框架
- **SQLite** - 轻量级数据库
- **WebSocket** - 实时通信
- **PySerial** - 串口通信库
- **Pydantic** - 数据验证
- **Loguru** - 日志管理

### 前端
- **React 18** - 用户界面库
- **TypeScript** - 类型安全的 JavaScript
- **Ant Design** - 企业级 UI 组件库
- **Vite** - 快速构建工具
- **React Router** - 路由管理
- **Zustand** - 状态管理

## 核心功能模块

### 1. 协议配置模块
- 可视化协议定义器
- 字段属性配置（偏移量、数据类型、比例系数）
- 协议导入/导出
- 数据模拟器

### 2. 界面设计模块
- 拖拽式控件布局
- 数据绑定配置
- 实时预览
- 样式自定义

### 3. 流程引擎模块
- 节点式流程设计
- 条件判断与循环
- 串口指令发送
- 界面控制逻辑

### 4. 串口通信模块
- 设备枚举与连接
- 实时数据接收
- 协议解析
- 数据推送

### 5. 工程管理模块
- 项目保存/加载
- 配置导入/导出
- 版本管理

## 开发计划

### 阶段1: 基础框架搭建 (1周)
- [x] 项目初始化
- [ ] 前端React应用搭建
- [ ] 后端FastAPI服务搭建
- [ ] 基础UI组件库
- [ ] 前后端通信机制

### 阶段2: 串口通信模块 (1周)
- [ ] 串口设备枚举
- [ ] 串口连接管理
- [ ] 数据接收与解析
- [ ] WebSocket实时推送

### 阶段3: 协议配置模块 (2周)
- [ ] 协议定义界面
- [ ] 字段配置组件
- [ ] 协议解析引擎
- [ ] 数据模拟器

### 阶段4: 界面设计模块 (2周)
- [ ] 控件库开发
- [ ] 拖拽布局系统
- [ ] 数据绑定机制
- [ ] 实时预览功能

### 阶段5: 流程引擎模块 (2周)
- [ ] 流程节点设计
- [ ] 流程编辑器
- [ ] 执行引擎
- [ ] 调试功能

### 阶段6: 系统集成与测试 (1周)
- [ ] 模块集成
- [ ] 端到端测试
- [ ] 性能优化
- [ ] 用户体验优化

## 项目结构

```
EC_Test_Create/
├── frontend/                 # React前端应用
│   ├── src/
│   │   ├── components/       # 通用组件
│   │   ├── pages/           # 页面组件
│   │   ├── stores/          # Zustand状态管理
│   │   ├── services/        # API服务
│   │   └── types/           # TypeScript类型定义
│   ├── public/
│   └── package.json
├── backend/                  # FastAPI后端服务
│   ├── app/
│   │   ├── api/             # API路由
│   │   ├── core/            # 核心配置
│   │   ├── models/          # 数据模型
│   │   ├── services/        # 业务逻辑
│   │   └── utils/           # 工具函数
│   ├── requirements.txt
│   └── main.py
├── docs/                     # 项目文档
│   ├── api.md               # API文档
│   ├── deployment.md        # 部署文档
│   └── user-guide.md        # 用户指南
├── shared/                   # 共享类型定义
└── README.md
```

## 快速开始

### 环境要求
- Node.js 18+
- Python 3.9+
- 现代浏览器

### 开发环境启动

1. 启动后端服务
```bash
cd backend
pip install -r requirements.txt
python main.py
```

2. 启动前端应用
```bash
cd frontend
npm install
npm run dev
```

3. 访问应用
打开浏览器访问 http://localhost:3000

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License